#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件夹管理Web路由模块
提供文件夹导出功能的API接口
"""

import os
import sys
import tempfile
import threading
import time
import json
from flask import Blueprint, request, jsonify, session
from werkzeug.utils import secure_filename
import logging

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入核心模块
try:
    from src.core.folder_manager import FolderManager
except ImportError:
    from core.folder_manager import FolderManager

# 配置日志
logger = logging.getLogger('FolderRoutes')

# 创建蓝图
folder_bp = Blueprint('folder', __name__, url_prefix='/api/folder')

# 全局变量存储任务进度
folder_tasks = {}
folder_lock = threading.Lock()

# 支持的Excel文件格式
ALLOWED_EXCEL_EXTENSIONS = {'xlsx', 'xls', 'xlsm'}

def allowed_excel_file(filename):
    """检查Excel文件扩展名是否被允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXCEL_EXTENSIONS


@folder_bp.route('/export', methods=['POST'])
def export_folders():
    """
    文件夹导出API
    接收SKU Excel文件、源路径、目标路径，执行文件夹导出
    """
    try:
        logger.info("收到文件夹导出请求")
        
        # 检查是否有Excel文件
        if 'excel_file' not in request.files:
            return jsonify({'success': False, 'error': '没有上传Excel文件'}), 400
        
        excel_file = request.files['excel_file']
        if excel_file.filename == '':
            return jsonify({'success': False, 'error': '没有选择Excel文件'}), 400
        
        if not allowed_excel_file(excel_file.filename):
            return jsonify({'success': False, 'error': '不支持的Excel文件格式'}), 400
        
        # 获取路径参数
        source_path = request.form.get('source_path', '').strip()
        export_path = request.form.get('export_path', '').strip()
        
        if not source_path or not export_path:
            return jsonify({'success': False, 'error': '缺少源路径或导出路径'}), 400
        
        # 验证路径存在
        if not os.path.exists(source_path):
            return jsonify({'success': False, 'error': f'源路径不存在: {source_path}'}), 400
        
        if not os.path.exists(export_path):
            return jsonify({'success': False, 'error': f'导出路径不存在: {export_path}'}), 400
        
        # 生成任务ID
        task_id = f"folder_export_{int(time.time())}"
        
        # 保存Excel文件到临时目录
        temp_dir = tempfile.mkdtemp(prefix=f"folder_export_{task_id}_")
        excel_path = os.path.join(temp_dir, secure_filename(excel_file.filename))
        excel_file.save(excel_path)
        
        # 初始化任务状态
        with folder_lock:
            folder_tasks[task_id] = {
                'status': 'starting',
                'progress': 0,
                'current_item': '',
                'total_items': 0,
                'processed_items': 0,
                'success_count': 0,
                'failed_count': 0,
                'errors': [],
                'start_time': time.time(),
                'export_path': '',
                'source_excel_path': excel_path
            }
        
        # 启动后台任务
        thread = threading.Thread(
            target=process_folder_export_task,
            args=(task_id, excel_path, source_path, export_path)
        )
        thread.daemon = True
        thread.start()
        
        logger.info(f"文件夹导出任务已启动: {task_id}")
        
        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '文件夹导出任务已启动'
        })
        
    except Exception as e:
        logger.error(f"文件夹导出API错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


@folder_bp.route('/progress/<task_id>')
def get_export_progress(task_id):
    """获取文件夹导出进度"""
    with folder_lock:
        if task_id not in folder_tasks:
            return jsonify({'success': False, 'error': '任务不存在'}), 404
        
        task_info = folder_tasks[task_id].copy()
        
        # 计算进度百分比
        if task_info['total_items'] > 0:
            progress_percent = int((task_info['processed_items'] / task_info['total_items']) * 100)
        else:
            progress_percent = 0
        
        task_info['progress_percent'] = progress_percent
        
        return jsonify({
            'success': True,
            'task_info': task_info
        })


@folder_bp.route('/set-workflow-paths', methods=['POST'])
def set_workflow_paths():
    """设置工作流路径信息"""
    try:
        data = request.json
        session['workflow_paths'] = {
            'export_path': data.get('export_path'),
            'source_excel_path': data.get('source_excel_path'),
            'from_folder_export': True
        }
        return jsonify({'success': True})
    except Exception as e:
        logger.error(f"设置工作流路径错误: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500


@folder_bp.route('/get-workflow-paths', methods=['GET'])
def get_workflow_paths():
    """获取工作流路径信息"""
    return jsonify(session.get('workflow_paths', {}))


def process_folder_export_task(task_id, excel_path, source_path, export_path):
    """
    处理文件夹导出任务（后台线程）
    
    Args:
        task_id: 任务ID
        excel_path: Excel文件路径
        source_path: 源文件夹路径
        export_path: 导出目标路径
    """
    try:
        logger.info(f"开始处理文件夹导出任务: {task_id}")
        
        # 创建文件夹管理器
        folder_manager = FolderManager()
        
        # 更新任务状态
        with folder_lock:
            folder_tasks[task_id]['status'] = 'reading_excel'
            folder_tasks[task_id]['current_item'] = '正在读取Excel文件...'
        
        # 读取SKU列表
        sku_list = folder_manager.read_sku_from_excel(excel_path)
        
        # 更新任务状态
        with folder_lock:
            folder_tasks[task_id]['status'] = 'processing'
            folder_tasks[task_id]['total_items'] = len(sku_list)
            folder_tasks[task_id]['current_item'] = f'开始处理 {len(sku_list)} 个SKU...'
        
        # 定义进度回调函数
        def progress_callback(current, total, current_item):
            with folder_lock:
                if task_id in folder_tasks:
                    folder_tasks[task_id]['processed_items'] = current
                    folder_tasks[task_id]['current_item'] = current_item
        
        # 执行文件夹复制
        copied_folders, errors, export_subfolder = folder_manager.copy_folders(
            source_path, export_path, sku_list, progress_callback
        )
        
        # 获取统计信息
        stats = folder_manager.get_export_stats()
        
        # 更新最终任务状态
        with folder_lock:
            folder_tasks[task_id].update({
                'status': 'completed',
                'success_count': len(copied_folders),
                'failed_count': len(errors),
                'errors': errors,
                'export_path': export_subfolder,
                'copied_folders': copied_folders,
                'total_time': stats.get('total_time', 0),
                'current_item': f'完成! 成功: {len(copied_folders)}, 失败: {len(errors)}'
            })
        
        logger.info(f"文件夹导出任务完成: {task_id}")
        
    except Exception as e:
        logger.error(f"文件夹导出任务失败: {task_id}, 错误: {str(e)}")
        with folder_lock:
            if task_id in folder_tasks:
                folder_tasks[task_id].update({
                    'status': 'failed',
                    'current_item': f'任务失败: {str(e)}',
                    'errors': [str(e)]
                })
    
    finally:
        # 清理临时文件
        try:
            if os.path.exists(excel_path):
                temp_dir = os.path.dirname(excel_path)
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
        except Exception as e:
            logger.warning(f"清理临时文件失败: {str(e)}")


def cleanup_old_tasks():
    """清理旧的任务记录"""
    current_time = time.time()
    with folder_lock:
        tasks_to_remove = []
        for task_id, task_info in folder_tasks.items():
            # 清理超过1小时的任务
            if current_time - task_info.get('start_time', 0) > 3600:
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del folder_tasks[task_id]


# 定期清理任务
def start_cleanup_timer():
    """启动定期清理定时器"""
    def cleanup_timer():
        while True:
            time.sleep(1800)  # 每30分钟清理一次
            cleanup_old_tasks()
    
    cleanup_thread = threading.Thread(target=cleanup_timer)
    cleanup_thread.daemon = True
    cleanup_thread.start()

# 启动清理定时器
start_cleanup_timer()
