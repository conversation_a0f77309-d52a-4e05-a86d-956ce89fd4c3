#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件夹管理核心模块
实现基于SKU列表的文件夹批量导出功能
"""

import os
import sys
import pandas as pd
import shutil
from pathlib import Path
import logging
import time
import datetime
from typing import List, Tuple, Dict, Optional

# 配置日志
logger = logging.getLogger('FolderManager')


class FolderManager:
    """
    文件夹管理器
    负责根据SKU列表批量导出文件夹
    """
    
    def __init__(self):
        """初始化文件夹管理器"""
        self.processed_folders = []
        self.failed_folders = []
        self.export_stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'start_time': None,
            'end_time': None
        }
    
    def read_sku_from_excel(self, excel_path: str) -> List[str]:
        """
        从Excel文件中读取SKU列表
        
        Args:
            excel_path: Excel文件路径
            
        Returns:
            SKU列表
        """
        try:
            logger.info(f"开始读取Excel文件: {excel_path}")
            
            # 尝试读取Excel文件
            df = pd.read_excel(excel_path)
            logger.info(f"Excel文件读取成功，共 {len(df)} 行数据")
            
            # 查找SKU列
            sku_column = None
            for col in df.columns:
                if 'SKU' in str(col).upper():
                    sku_column = col
                    break
            
            if sku_column is None:
                raise ValueError("未找到SKU列，请确保Excel文件包含名为'SKU'的列")
            
            logger.info(f"找到SKU列: {sku_column}")
            
            # 提取SKU数据并清理
            sku_list = df[sku_column].dropna().astype(str).str.strip().tolist()
            sku_list = [sku for sku in sku_list if sku and sku != 'nan']
            
            logger.info(f"成功提取 {len(sku_list)} 个有效SKU")
            return sku_list
            
        except Exception as e:
            error_msg = f"读取Excel文件失败: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def find_sku_folder(self, root_dir: str, sku: str) -> Optional[str]:
        """
        递归搜索包含指定SKU的文件夹
        
        Args:
            root_dir: 搜索的根目录
            sku: 要搜索的SKU字符串
            
        Returns:
            找到的文件夹路径，如果未找到则返回None
        """
        sku = str(sku).strip()
        logger.debug(f"开始搜索SKU: {sku}")
        
        # 首先检查直接匹配（传统方式）
        direct_path = os.path.join(root_dir, sku)
        if os.path.isdir(direct_path):
            logger.debug(f"直接匹配成功: {direct_path}")
            return direct_path
            
        # 如果没有直接匹配，开始递归搜索
        try:
            for root, dirs, files in os.walk(root_dir):
                for dir_name in dirs:
                    # 检查完全匹配
                    if dir_name == sku:
                        full_path = os.path.join(root, dir_name)
                        logger.debug(f"找到SKU文件夹: {full_path}")
                        return full_path
        except Exception as e:
            logger.error(f"搜索SKU {sku} 时发生错误: {str(e)}")
        
        logger.warning(f"未找到SKU文件夹: {sku}")
        return None
    
    def create_export_subfolder(self, export_path: str) -> str:
        """
        在导出目标文件夹中创建一个新的子文件夹用于存放所有复制的文件夹
        
        Args:
            export_path: 导出目标路径
            
        Returns:
            创建的子文件夹路径
        """
        now = datetime.datetime.now()
        subfolder_name = f"导出SKU_{now.strftime('%Y%m%d_%H%M%S')}"
        subfolder_path = os.path.join(export_path, subfolder_name)
        
        try:
            os.makedirs(subfolder_path, exist_ok=True)
            logger.info(f"创建导出子文件夹: {subfolder_path}")
            return subfolder_path
        except Exception as e:
            error_msg = f"创建导出子文件夹时发生错误: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    def copy_folders(self, source_path: str, export_path: str, sku_list: List[str], 
                    progress_callback=None) -> Tuple[List[str], List[str], str]:
        """
        查找并复制指定SKU的文件夹及其内容到目标位置
        
        Args:
            source_path: 源文件夹路径
            export_path: 导出目标路径
            sku_list: SKU列表
            progress_callback: 进度回调函数
            
        Returns:
            (成功复制的文件夹列表, 错误列表, 导出子文件夹路径)
        """
        copied_folders = []
        errors = []
        total_folders = len(sku_list)
        
        # 初始化统计信息
        self.export_stats = {
            'total': total_folders,
            'success': 0,
            'failed': 0,
            'start_time': time.time(),
            'end_time': None
        }
        
        logger.info(f"开始查找和复制文件夹，共 {total_folders} 个SKU")
        
        # 在导出目标文件夹中创建一个新的子文件夹用于存放所有复制的文件夹
        export_subfolder = self.create_export_subfolder(export_path)
        logger.info(f"所有SKU文件夹将被复制到: {export_subfolder}")
        
        for i, sku in enumerate(sku_list):
            try:
                # 确保SKU是字符串
                sku_str = str(sku).strip()
                if not sku_str:
                    continue
                
                # 更新进度
                if progress_callback:
                    progress_callback(i, total_folders, f"正在查找: {sku_str}")
                
                logger.info(f"处理 ({i+1}/{total_folders}): {sku_str}")
                
                # 查找SKU对应的文件夹
                source_folder = self.find_sku_folder(source_path, sku_str)
                
                # 检查源文件夹是否找到
                if not source_folder:
                    error_msg = f"源文件夹 '{sku_str}' 不存在"
                    errors.append(error_msg)
                    logger.warning(error_msg)
                    self.export_stats['failed'] += 1
                    continue
                
                # 目标文件夹路径 (在新创建的子文件夹中)
                target_folder = os.path.join(export_subfolder, sku_str)
                
                # 检查目标文件夹是否已存在
                if os.path.exists(target_folder):
                    error_msg = f"目标位置已存在文件夹 '{sku_str}'"
                    errors.append(error_msg)
                    logger.warning(error_msg)
                    self.export_stats['failed'] += 1
                    continue
                
                # 更新状态
                if progress_callback:
                    progress_callback(i, total_folders, f"正在复制: {sku_str}")
                
                # 复制文件夹及其内容
                logger.info(f"开始复制文件夹: {sku_str}")
                copy_start = time.time()
                shutil.copytree(source_folder, target_folder)
                copy_end = time.time()
                copy_time = copy_end - copy_start
                
                logger.info(f"文件夹 {sku_str} 复制完成，耗时: {copy_time:.2f}秒")
                copied_folders.append(sku_str)
                self.export_stats['success'] += 1
                
            except Exception as e:
                error_msg = f"复制文件夹 '{sku_str}' 时发生错误：{str(e)}"
                errors.append(error_msg)
                logger.error(error_msg, exc_info=True)
                self.export_stats['failed'] += 1
        
        # 完成所有复制
        self.export_stats['end_time'] = time.time()
        total_time = self.export_stats['end_time'] - self.export_stats['start_time']
        logger.info(f"所有文件夹复制完成，总耗时: {total_time:.2f}秒")
        
        # 更新最终进度
        if progress_callback:
            progress_callback(total_folders, total_folders, f"完成! 总耗时: {total_time:.2f}秒")
        
        return copied_folders, errors, export_subfolder
    
    def get_export_stats(self) -> Dict:
        """
        获取导出统计信息
        
        Returns:
            统计信息字典
        """
        stats = self.export_stats.copy()
        if stats['start_time'] and stats['end_time']:
            stats['total_time'] = stats['end_time'] - stats['start_time']
        else:
            stats['total_time'] = 0
        return stats
