/**
 * 亚马逊图片上传图床工具 - Web版本
 * 前端JavaScript应用
 */

class AmazonImageUploader {
    constructor() {
        this.selectedFiles = [];
        this.currentTaskId = null;
        this.progressInterval = null;
        this.apiBaseUrl = '';
        this.parsedUrls = null;
        this.currentMode = 'upload'; // 'upload' 或 'parse'

        // 文件夹管理相关
        this.folderTaskId = null;
        this.folderProgressInterval = null;
        this.workflowPaths = null;

        this.init();
    }

    init() {
        // 检查Bootstrap是否正确加载
        if (typeof bootstrap === 'undefined') {
            console.error('❌ Bootstrap JavaScript未加载！');
            console.log('💡 请检查Bootstrap文件是否正确引用');
            console.log('🔄 系统将自动尝试CDN备用方案');
        } else {
            console.log('✅ Bootstrap JavaScript已加载');
        }
        
        this.bindEvents();
        this.loadInitialData();
    }

    bindEvents() {
        // 模式切换事件
        const uploadMethodRadio = document.getElementById('uploadMethod');
        const parseMethodRadio = document.getElementById('parseMethod');

        if (uploadMethodRadio) {
            uploadMethodRadio.addEventListener('change', () => {
                if (uploadMethodRadio.checked) {
                    this.switchMode('upload');
                }
            });
        }

        if (parseMethodRadio) {
            parseMethodRadio.addEventListener('change', () => {
                if (parseMethodRadio.checked) {
                    this.switchMode('parse');
                }
            });
        }

        // 文件上传相关事件
        const fileDropZone = document.getElementById('fileDropZone');
        const fileInput = document.getElementById('fileInput');  // 文件夹选择
        const filesInput = document.getElementById('filesInput'); // 单个文件选择
        const selectFolderBtn = document.getElementById('selectFolderBtn');
        const selectFilesBtn = document.getElementById('selectFilesBtn');
        const confirmFolderBtn = document.getElementById('confirmFolderBtn');
        const startProcessBtn = document.getElementById('startProcessBtn');
        const uploadFolderInput = document.getElementById('uploadFolder');

        if (fileDropZone) {
            fileDropZone.addEventListener('dragover', this.handleDragOver.bind(this));
            fileDropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
            fileDropZone.addEventListener('drop', this.handleDrop.bind(this));
        }

        if (selectFolderBtn) {
            selectFolderBtn.addEventListener('click', () => fileInput.click());
        }

        if (selectFilesBtn) {
            selectFilesBtn.addEventListener('click', () => filesInput.click());
        }

        if (fileInput) {
            fileInput.addEventListener('change', this.handleFolderSelect.bind(this));
        }

        if (filesInput) {
            filesInput.addEventListener('change', this.handleFileSelect.bind(this));
        }

        if (uploadFolderInput) {
            uploadFolderInput.addEventListener('input', this.validateUploadFolder.bind(this));
        }

        if (confirmFolderBtn) {
            confirmFolderBtn.addEventListener('click', this.confirmUploadFolder.bind(this));
        }

        if (startProcessBtn) {
            startProcessBtn.addEventListener('click', this.startProcess.bind(this));
        }

        // URL映射相关事件
        const parseUrlBtn = document.getElementById('parseUrlBtn');
        const clearUrlBtn = document.getElementById('clearUrlBtn');
        const generateUrlExcelBtn = document.getElementById('generateUrlExcelBtn');

        if (parseUrlBtn) {
            parseUrlBtn.addEventListener('click', this.parseUrls.bind(this));
        }

        if (generateUrlExcelBtn) {
            generateUrlExcelBtn.addEventListener('click', this.generateUrlExcel.bind(this));
        }

        if (clearUrlBtn) {
            clearUrlBtn.addEventListener('click', this.clearUrls.bind(this));
        }

        // 历史记录相关事件
        const refreshHistoryBtn = document.getElementById('refreshHistory');
        if (refreshHistoryBtn) {
            refreshHistoryBtn.addEventListener('click', this.loadHistoryFiles.bind(this));
        }

        // 系统测试相关事件
        const testApiBtn = document.getElementById('testApiBtn');
        if (testApiBtn) {
            testApiBtn.addEventListener('click', this.testApiConnection.bind(this));
        }

        // 模板填充相关事件
        const fillTemplateBtn = document.getElementById('fillTemplateBtn');
        const clearTemplateBtn = document.getElementById('clearTemplateBtn');
        const templateFile = document.getElementById('templateFile');
        const reportFile = document.getElementById('reportFile');
        const mappingFile = document.getElementById('mappingFile');
        const productInfoFile = document.getElementById('productInfoFile');

        if (fillTemplateBtn) {
            fillTemplateBtn.addEventListener('click', this.handleFillTemplate.bind(this));
        }

        if (clearTemplateBtn) {
            clearTemplateBtn.addEventListener('click', this.clearTemplateFiles.bind(this));
        }

        // 文件选择监听，用于启用/禁用填充按钮
        [templateFile, reportFile, mappingFile].forEach(fileInput => {
            if (fileInput) {
                fileInput.addEventListener('change', this.validateTemplateFiles.bind(this));
            }
        });

        // 更新方式选择监听
        const updateModeRadios = document.querySelectorAll('input[name="updateMode"]');
        updateModeRadios.forEach(radio => {
            if (radio) {
                radio.addEventListener('change', this.handleUpdateModeChange.bind(this));
            }
        });

        // Tab切换事件
        const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
        console.log('[初始化调试] 找到Tab按钮数量:', tabButtons.length);
        tabButtons.forEach((button, index) => {
            console.log(`[初始化调试] Tab按钮 ${index + 1}:`, button.getAttribute('data-bs-target'));
            // 使用Bootstrap 5的正确事件名称
            button.addEventListener('shown.bs.tab', this.handleTabChange.bind(this));
            button.addEventListener('click', (e) => {
                console.log('[Tab调试] Tab按钮点击:', e.target.getAttribute('data-bs-target'));
            });
        });
    }

    async loadInitialData() {
        // 加载历史文件列表
        this.loadHistoryFiles();

        // 检查工作流路径
        await this.checkWorkflowPaths();
    }

    async checkWorkflowPaths() {
        try {
            const response = await fetch('/api/folder/get-workflow-paths');
            const result = await response.json();

            if (result.from_folder_export && result.export_path) {
                this.workflowPaths = result;
                this.showWorkflowHint();
            }
        } catch (error) {
            console.log('检查工作流路径失败:', error);
        }
    }

    showWorkflowHint() {
        if (!this.workflowPaths) return;

        // 在页面顶部显示工作流提示
        const hintHtml = `
            <div class="alert alert-info alert-dismissible fade show" id="workflowHint" role="alert">
                <i class="bi bi-info-circle"></i>
                <strong>工作流连接提示：</strong>
                检测到来自文件夹导出的路径信息，已自动设置默认路径。
                <br>
                <small>
                    📁 图片目录：${this.workflowPaths.export_path}<br>
                    📄 Excel文件：${this.workflowPaths.source_excel_path || '未设置'}
                </small>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // 在主容器顶部插入提示
        const mainContainer = document.querySelector('.main-container');
        if (mainContainer) {
            mainContainer.insertAdjacentHTML('afterbegin', hintHtml);
        }
    }

    /**
     * 切换功能模式
     * @param {string} mode - 'upload' 或 'parse'
     */
    switchMode(mode) {
        console.log(`🔄 切换到${mode}模式`);
        this.currentMode = mode;

        const uploadSection = document.getElementById('uploadSection');
        const parseSection = document.getElementById('parseSection');
        const uploadSettings = document.getElementById('uploadSettings');
        const taskListStepTitle = document.getElementById('taskListStepTitle');
        const processButtonText = document.getElementById('processButtonText');
        const processHintText = document.getElementById('processHintText');

        if (mode === 'upload') {
            // 显示上传相关界面
            if (uploadSection) uploadSection.style.display = 'block';
            if (parseSection) parseSection.style.display = 'none';

            // 更新文本
            if (taskListStepTitle) taskListStepTitle.textContent = '第三步：选择商品信息文件 (可选)';
            if (processButtonText) processButtonText.innerHTML = '<i class="bi bi-cloud-upload"></i> 开始上传';
            if (processHintText) processHintText.textContent = '不选择文件将生成基础映射表';

        } else if (mode === 'parse') {
            // 显示解析相关界面
            if (uploadSection) uploadSection.style.display = 'none';
            if (parseSection) parseSection.style.display = 'block';
            if (uploadSettings) uploadSettings.style.display = 'none'; // 解析模式不需要上传设置

            // 更新文本
            if (taskListStepTitle) taskListStepTitle.textContent = '第二步：选择商品信息文件 (可选)';
            if (processButtonText) processButtonText.innerHTML = '<i class="bi bi-file-earmark-spreadsheet"></i> 生成映射表';
            if (processHintText) processHintText.textContent = '不选择文件将生成基础映射表';
        }

        // 重置状态
        this.resetState();
    }

    /**
     * 重置状态
     */
    resetState() {
        this.selectedFiles = [];
        this.parsedUrls = null;
        this.currentTaskId = null;

        // 隐藏各种结果显示
        const selectedFiles = document.getElementById('selectedFiles');
        const taskListSection = document.getElementById('taskListSection');
        const resultCard = document.getElementById('resultCard');
        const urlParseSteps = document.getElementById('urlParseSteps');

        if (selectedFiles) selectedFiles.style.display = 'none';
        if (taskListSection) taskListSection.style.display = 'none';
        if (resultCard) resultCard.style.display = 'none';
        if (urlParseSteps) urlParseSteps.style.display = 'none';

        // 清空输入
        const urlInput = document.getElementById('urlInput');
        const taskListFile = document.getElementById('taskListFile');
        if (urlInput) urlInput.value = '';
        if (taskListFile) taskListFile.value = '';
    }

    // 文件拖拽处理
    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();

        const dropZone = e.currentTarget;
        dropZone.classList.add('dragover');

        // 检查拖拽的内容类型
        const hasFiles = e.dataTransfer.types.includes('Files');
        if (hasFiles) {
            // 更新拖拽提示文本
            const heading = dropZone.querySelector('h5');
            if (heading && !heading.dataset.originalText) {
                heading.dataset.originalText = heading.textContent;
                heading.innerHTML = '<i class="bi bi-download text-success"></i> 松开鼠标释放文件夹';
            }
        }
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();

        const dropZone = e.currentTarget;

        // 检查是否真的离开了拖拽区域（避免子元素触发）
        if (!dropZone.contains(e.relatedTarget)) {
            dropZone.classList.remove('dragover');

            // 恢复原始文本
            const heading = dropZone.querySelector('h5');
            if (heading && heading.dataset.originalText) {
                heading.innerHTML = heading.dataset.originalText;
                delete heading.dataset.originalText;
            }
        }
    }

    async handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();

        const dropZone = e.currentTarget;
        dropZone.classList.remove('dragover');

        // 恢复原始文本
        const heading = dropZone.querySelector('h5');
        if (heading && heading.dataset.originalText) {
            heading.innerHTML = heading.dataset.originalText;
            delete heading.dataset.originalText;
        }

        console.log('[拖拽调试] 开始处理拖拽事件');

        // 显示处理中状态
        this.showNotification('info', '正在处理拖拽的文件夹，请稍候...');

        try {
            // 检查是否支持文件夹拖拽
            if (e.dataTransfer.items) {
                console.log('[拖拽调试] 使用DataTransferItemList处理');
                await this.handleDropWithItems(e.dataTransfer.items);
            } else {
                console.log('[拖拽调试] 使用传统文件列表处理');
                const files = Array.from(e.dataTransfer.files);
                this.processSelectedFiles(files, '拖拽的文件');
            }
        } catch (error) {
            console.error('[拖拽调试] 处理拖拽时出错:', error);
            this.showNotification('error', '处理拖拽文件时出错: ' + error.message);
        }
    }

    /**
     * 处理拖拽的文件夹和文件（支持嵌套目录）
     */
    async handleDropWithItems(items) {
        const files = [];
        const promises = [];

        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            if (item.kind === 'file') {
                const entry = item.webkitGetAsEntry();
                if (entry) {
                    promises.push(this.traverseFileTree(entry, files));
                }
            }
        }

        try {
            await Promise.all(promises);
            console.log(`[拖拽调试] 总共收集到 ${files.length} 个文件`);

            if (files.length > 0) {
                this.processSelectedFiles(files, '拖拽的文件夹');
            } else {
                this.showNotification('warning', '拖拽的文件夹中没有找到图片文件');
            }
        } catch (error) {
            console.error('[拖拽调试] 处理文件夹时出错:', error);
            this.showNotification('error', '处理拖拽文件夹时出错: ' + error.message);
        }
    }

    /**
     * 递归遍历文件树（支持嵌套文件夹）
     */
    async traverseFileTree(item, files, path = '') {
        return new Promise((resolve, reject) => {
            if (item.isFile) {
                // 处理文件
                item.file((file) => {
                    // 只处理图片文件
                    const isImage = file.type.startsWith('image/') ||
                        file.name.toLowerCase().match(/\.(jpg|jpeg|png|gif|bmp|webp)$/);

                    if (isImage) {
                        // 添加路径信息到文件对象
                        file.relativePath = path + file.name;
                        files.push(file);
                        console.log(`[拖拽调试] 找到图片文件: ${file.relativePath}`);
                    }
                    resolve();
                }, reject);
            } else if (item.isDirectory) {
                // 处理目录
                const dirReader = item.createReader();
                const newPath = path + item.name + '/';

                dirReader.readEntries(async (entries) => {
                    try {
                        const subPromises = entries.map(entry =>
                            this.traverseFileTree(entry, files, newPath)
                        );
                        await Promise.all(subPromises);
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                }, reject);
            } else {
                resolve();
            }
        });
    }

    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.processSelectedFiles(files, '选择的文件');
    }

    handleFolderSelect(e) {
        const files = Array.from(e.target.files);
        this.processSelectedFiles(files, '文件夹中的图片');
    }

    processSelectedFiles(files, source = '选择的文件') {
        console.log(`[前端调试] 开始处理 ${files.length} 个文件，来源: ${source}`);

        // 筛选有效的图片文件
        const validFiles = [];
        const invalidFiles = [];

        files.forEach((file, index) => {
            console.log(`[前端调试] 处理文件 ${index + 1}/${files.length}: ${file.name}`);

            // 获取文件路径信息（用于调试）
            const filePath = file.relativePath || file.webkitRelativePath || file.name;
            console.log(`[前端调试] 文件路径: ${filePath}`);

            const isImage = file.type.startsWith('image/') ||
                file.name.toLowerCase().match(/\.(jpg|jpeg|png|gif|bmp|webp)$/);

            if (!isImage) {
                console.log(`[前端调试] 跳过非图片文件: ${file.name}`);
                invalidFiles.push({ file, reason: '不是图片格式' });
                return;
            }

            // 检查单个文件大小（不超过20MB）
            if (file.size > 20 * 1024 * 1024) {
                console.log(`[前端调试] 文件过大: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`);
                invalidFiles.push({ file, reason: '单个文件不能超过20MB，请先压缩图片' });
                return;
            }

            const validationResult = this.validateFilename(file.name);
            if (!validationResult.valid) {
                console.log(`[前端调试] 文件名验证失败: ${file.name} - ${validationResult.reason}`);
                invalidFiles.push({ file, reason: validationResult.reason });
                return;
            } else {
                console.log(`[前端调试] 文件名验证通过: ${file.name}`);
            }

            validFiles.push(file);
        });

        // 计算总文件大小（仅用于显示，不限制）
        const totalSize = validFiles.reduce((total, file) => total + file.size, 0);
        console.log(`[前端调试] 验证结果 - 有效文件: ${validFiles.length}, 无效文件: ${invalidFiles.length}`);
        console.log(`[前端调试] 总文件大小: ${this.formatFileSize(totalSize)}`);

        // 移除总文件大小限制，图床支持任意大小的文件夹上传
        // 但仍保留单个文件20MB的限制以确保上传稳定性

        // 显示处理结果
        if (invalidFiles.length > 0) {
            console.log(`[前端调试] 发现 ${invalidFiles.length} 个无效文件`);
            let message = `${source}中有 ${invalidFiles.length} 个文件不符合要求：\n`;
            invalidFiles.slice(0, 5).forEach(item => {
                message += `• ${item.file.name}: ${item.reason}\n`;
            });
            if (invalidFiles.length > 5) {
                message += `... 等共 ${invalidFiles.length} 个文件\n`;
            }
            message += '\n文件名要求：ASIN_类型.扩展名 (如: B07XXXXX_MAIN.jpg)';

            if (validFiles.length > 0) {
                message += `\n\n将继续处理 ${validFiles.length} 个有效文件`;
                console.log(`[前端调试] ✅ 发现 ${validFiles.length} 个有效文件，自动继续处理`);
                this.showNotification('warning', message);
                console.log(`[前端调试] ✅ 自动继续处理 ${validFiles.length} 个有效文件`);
            } else {
                console.log(`[前端调试] ❌ 没有有效文件，显示错误提示`);
                // 使用更友好的错误提示方式
                this.showDetailedErrorMessage(message, invalidFiles);
                return;
            }
        }

        this.selectedFiles = validFiles;
        console.log(`[前端调试] 设置选中文件列表，共 ${validFiles.length} 个文件`);

        if (validFiles.length > 0) {
            console.log(`[前端调试] ✅ 文件选择成功，准备显示成功提示`);
            this.showNotification('success', `成功选择 ${validFiles.length} 个有效图片文件`);

            // 如果文件数量较少，给出提示
            if (validFiles.length <= 350) {
                this.showNotification('info', `图片数量≤350，建议直接到图床上传以获得更好的体验`);
            }
        }

        console.log(`[前端调试] 调用 displaySelectedFiles() 显示文件列表`);
        this.displaySelectedFiles();
        console.log(`[前端调试] 调用 showUploadSettings() 显示上传设置`);
        this.showUploadSettings();
    }

    showUploadSettings() {
        if (this.currentMode === 'upload' && this.selectedFiles.length > 0) {
            document.getElementById('uploadSettings').style.display = 'block';
            document.getElementById('uploadFolder').focus();

            // 显示文件选择完成的提示
            this.showNotification('info', `已选择 ${this.selectedFiles.length} 个文件，请在下方设置上传目录`);
        } else {
            document.getElementById('uploadSettings').style.display = 'none';
            if (this.currentMode === 'upload') {
                document.getElementById('taskListSection').style.display = 'none';
            }
        }
    }

    validateUploadFolder() {
        const uploadFolder = document.getElementById('uploadFolder').value.trim();
        const confirmBtn = document.getElementById('confirmFolderBtn');

        // 验证目录名：不能为空，不能包含 / 或 \
        const isValid = uploadFolder && !uploadFolder.includes('/') && !uploadFolder.includes('\\');

        if (confirmBtn) {
            confirmBtn.disabled = !isValid;
        }

        return isValid;
    }

    confirmUploadFolder() {
        if (!this.validateUploadFolder()) {
            this.showNotification('error', '目录名不能为空，且不能包含 / 或 \\ 字符！');
            return;
        }

        const uploadFolder = document.getElementById('uploadFolder').value.trim();

        // 自动确认并进入下一步
        const confirmMessage = `设置完成：\n\n` +
            `• 上传目录：${uploadFolder}\n` +
            `• 图片数量：${this.selectedFiles.length} 个\n` +
            `• 文件总大小：${this.getTotalFileSize()}`;

        // 直接进入下一步，不需要用户确认
        document.getElementById('uploadSettings').style.display = 'none';
        document.getElementById('taskListSection').style.display = 'block';
        this.showNotification('success', `✅ 上传目录已设置：${uploadFolder}，现在可以选择任务列表文件（可选）或直接开始上传`);

        // 滚动到任务列表区域
        document.getElementById('taskListSection').scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
    }

    validateFilename(filename) {
        // 亚马逊图片文件名格式验证 - 完全与桌面版本保持一致
        const upperFilename = filename.toUpperCase();

        // 1. 必须以B0开头（Amazon ASIN格式）
        if (!upperFilename.startsWith('B0')) {
            return { valid: false, reason: "文件名必须以B0开头（Amazon ASIN格式）" };
        }

        // 2. 必须包含MAIN、PT或SWCH关键词
        const hasMain = upperFilename.includes('MAIN');
        const hasSwch = upperFilename.includes('SWCH');
        // 修改PT验证逻辑，使其与桌面版本完全一致
        const hasPT = upperFilename.includes('PT');

        if (!(hasMain || hasSwch || hasPT)) {
            return { valid: false, reason: "文件名必须包含MAIN、PT或SWCH关键词以标识图片类型" };
        }

        // 3. 必须是支持的图片格式
        const supportedExtensions = ['.PNG', '.JPG', '.JPEG', '.GIF', '.WEBP', '.BMP'];
        const hasValidExtension = supportedExtensions.some(ext => upperFilename.endsWith(ext));

        if (!hasValidExtension) {
            return { valid: false, reason: "不支持的图片格式" };
        }

        return { valid: true, reason: "文件名格式正确" };
    }

    displaySelectedFiles() {
        const selectedFilesDiv = document.getElementById('selectedFiles');
        const fileListDiv = document.getElementById('fileList');

        if (this.selectedFiles.length === 0) {
            selectedFilesDiv.style.display = 'none';
            return;
        }

        selectedFilesDiv.style.display = 'block';
        fileListDiv.innerHTML = '';

        // 添加统计信息
        const statsDiv = document.createElement('div');
        statsDiv.className = 'alert alert-info mb-3';
        statsDiv.innerHTML = `
            <strong><i class="bi bi-info-circle"></i> 已选择 ${this.selectedFiles.length} 个图片文件</strong>
            <small class="d-block mt-1">总大小: ${this.getTotalFileSize()}</small>
        `;
        fileListDiv.appendChild(statsDiv);

        this.selectedFiles.forEach((file, index) => {
            const fileItem = this.createFileListItem(file, index);
            fileListDiv.appendChild(fileItem);
        });
    }

    createFileListItem(file, index) {
        const fileItem = document.createElement('div');
        fileItem.className = 'list-group-item d-flex justify-content-between align-items-center';

        const fileInfo = document.createElement('div');
        fileInfo.innerHTML = `
            <strong>${file.name}</strong>
            <small class="text-muted d-block">${this.formatFileSize(file.size)}</small>
        `;

        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn btn-sm btn-outline-danger';
        removeBtn.innerHTML = '<i class="bi bi-trash"></i>';
        removeBtn.onclick = () => this.removeFile(index);

        fileItem.appendChild(fileInfo);
        fileItem.appendChild(removeBtn);

        return fileItem;
    }

    removeFile(index) {
        this.selectedFiles.splice(index, 1);
        this.displaySelectedFiles();
        this.showUploadSettings();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    getTotalFileSize() {
        const totalBytes = this.selectedFiles.reduce((total, file) => total + file.size, 0);
        return this.formatFileSize(totalBytes);
    }

    // 开始处理（上传或解析）
    async startProcess() {
        if (this.currentMode === 'upload') {
            await this.startUpload();
        } else if (this.currentMode === 'parse') {
            await this.startUrlParsing();
        }
    }

    // 上传处理
    async startUpload() {
        if (this.selectedFiles.length === 0) {
            this.showNotification('warning', '请先选择要上传的文件');
            return;
        }

        // 上传前确认 - 与桌面版本保持一致
        const uploadFolder = document.getElementById('uploadFolder').value || 'DEFAULT';
        const taskListFile = document.getElementById('taskListFile').files[0];

        let confirmMessage = `上传前请确认图片命名规则：\n\n`;
        confirmMessage += `1. 文件名以B0开头（Amazon ASIN格式）\n`;
        confirmMessage += `2. 主图请包含MAIN关键词\n`;
        confirmMessage += `3. 附图请包含PT01-PT08关键词\n`;
        confirmMessage += `4. 色卡图请包含SWCH关键词\n\n`;
        confirmMessage += `即将上传 ${this.selectedFiles.length} 个图片文件到目录: ${uploadFolder}\n\n`;
        confirmMessage += `使用高性能设置：\n`;
        confirmMessage += `- 智能并发线程数: 8-12个（根据文件数量动态调整）\n`;
        confirmMessage += `- 超快上传延迟: 0.05秒\n`;
        confirmMessage += `- 保持原始图片质量\n`;
        confirmMessage += `- 保留原始文件名（UPLOAD_MODE=2）\n`;
        if (taskListFile) {
            confirmMessage += `- 任务列表文件: ${taskListFile.name}\n`;
        }
        confirmMessage += `\n开始上传...`;

        // 显示上传信息并直接开始上传
        this.showNotification('info', confirmMessage);
        this.showLoading(true);

        const formData = new FormData();
        this.selectedFiles.forEach(file => {
            formData.append('files', file);
        });

        formData.append('upload_folder', uploadFolder);

        // 添加图片任务列表文件（如果选择了）
        if (taskListFile) {
            formData.append('task_list_file', taskListFile);
        }

        try {
            const response = await fetch('/api/upload-images', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.currentTaskId = result.task_id;
                this.showProgressCard();
                this.startProgressTracking();
                this.showNotification('success', '上传任务已开始');
            } else {
                this.showNotification('error', '上传失败: ' + result.error);
            }
        } catch (error) {
            this.showNotification('error', '上传请求失败: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    showProgressCard() {
        const progressCard = document.getElementById('progressCard');
        const resultCard = document.getElementById('resultCard');

        if (progressCard) progressCard.style.display = 'block';
        if (resultCard) resultCard.style.display = 'none';

        // 初始化进度显示
        this.updateProgressDisplay({
            total: this.selectedFiles.length,
            completed: 0,
            failed: 0
        });
    }

    startProgressTracking() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        this.progressInterval = setInterval(async () => {
            try {
                const response = await fetch(`/api/upload-progress/${this.currentTaskId}`);
                const result = await response.json();

                if (result.success) {
                    this.updateProgressDisplay(result.progress);

                    const status = result.progress.status;
                    if (status === 'completed' || status === 'completed_with_errors') {
                        clearInterval(this.progressInterval);
                        this.showResults(result.progress);
                    } else if (status === 'failed') {
                        clearInterval(this.progressInterval);
                        this.showNotification('error', '上传任务失败: ' + (result.progress.error || '未知错误'));
                    }
                }
            } catch (error) {
                console.error('获取进度失败:', error);
            }
        }, 2000); // 改为2秒查询一次，减少服务器压力
    }

    // URL解析处理
    async startUrlParsing() {
        const urlInput = document.getElementById('urlInput');
        if (!urlInput) return;

        const urlText = urlInput.value.trim();
        if (!urlText) {
            this.showNotification('warning', '请输入URL列表');
            return;
        }

        // 先解析URL
        await this.parseUrls();

        // 如果解析成功，直接生成Excel
        if (this.parsedUrls && this.parsedUrls.length > 0) {
            await this.generateUrlExcel();
        }
    }

    updateProgressDisplay(progress) {
        const completed = progress.completed + progress.failed;
        const total = progress.total;
        const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

        // 更新进度条
        const progressBar = document.getElementById('progressBar');
        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }

        // 更新进度文本
        const progressText = document.getElementById('progressText');
        if (progressText) {
            progressText.textContent = `处理中... ${completed}/${total}`;
        }

        // 更新计数器
        const successCount = document.getElementById('successCount');
        const failCount = document.getElementById('failCount');
        const totalCount = document.getElementById('totalCount');

        if (successCount) successCount.textContent = progress.completed;
        if (failCount) failCount.textContent = progress.failed;
        if (totalCount) totalCount.textContent = progress.total;
    }

    showResults(data) {
        const progressCard = document.getElementById('progressCard');
        const resultCard = document.getElementById('resultCard');
        const resultTitle = document.getElementById('resultTitle');
        const resultAsinCount = document.getElementById('resultAsinCount');
        const resultTotalCount = document.getElementById('resultTotalCount');
        const downloadResultExcel = document.getElementById('downloadResultExcel');

        if (progressCard) progressCard.style.display = 'none';
        if (resultCard) resultCard.style.display = 'block';

        if (this.currentMode === 'upload') {
            // 上传模式显示
            if (resultTitle) resultTitle.textContent = '上传完成';
            if (resultAsinCount) resultAsinCount.textContent = data.total_asins || 0;
            if (resultTotalCount) resultTotalCount.textContent = data.completed || 0;

            // 处理下载按钮
            if (downloadResultExcel && data.excel_file) {
                downloadResultExcel.onclick = () => {
                    window.open(`/api/download-history/${data.excel_file}`, '_blank');
                };
            }
        } else if (this.currentMode === 'parse') {
            // 解析模式显示
            if (resultTitle) resultTitle.textContent = '映射表生成完成';
            if (resultAsinCount) resultAsinCount.textContent = data.total_asins || 0;
            if (resultTotalCount) resultTotalCount.textContent = data.total_urls || 0;

            // 处理下载按钮
            if (downloadResultExcel && data.excel_file) {
                downloadResultExcel.onclick = () => {
                    window.open(`/api/download-history/${data.excel_file}`, '_blank');
                };
            }
        }

        // 刷新历史记录
        this.loadHistoryFiles();
    }

    createResultSummary(progress) {
        const summary = document.createElement('div');
        summary.className = 'alert alert-info';
        summary.innerHTML = `
            <h6><i class="bi bi-info-circle"></i> 上传完成</h6>
            <p class="mb-0">成功: ${progress.completed} | 失败: ${progress.failed} | 总计: ${progress.total}</p>
        `;
        return summary;
    }

    createResultItem(result) {
        const resultItem = document.createElement('div');
        resultItem.className = `result-item ${result.success ? '' : 'error'}`;

        const statusBadge = result.success ?
            '<span class="status-badge status-success">上传成功</span>' :
            '<span class="status-badge status-error">上传失败</span>';

        const detail = result.success ?
            `<small class="text-muted d-block mt-1">URL: <a href="${result.url}" target="_blank">${result.url}</a></small>` :
            `<small class="text-danger d-block mt-1">错误: ${result.error}</small>`;

        resultItem.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <strong>${result.filename}</strong>
                    <div class="mt-1">${statusBadge}</div>
                    ${detail}
                </div>
            </div>
        `;

        return resultItem;
    }

    handleDownloadButton(progress) {
        const downloadSection = document.getElementById('downloadSection');
        const downloadBtn = document.getElementById('downloadExcel');

        if (progress.excel_file && downloadSection && downloadBtn) {
            downloadSection.style.display = 'block';
            downloadBtn.onclick = () => {
                window.open(`/api/download-excel/${this.currentTaskId}`, '_blank');
            };
        }
    }

    clearFileSelection() {
        this.selectedFiles = [];
        const fileInput = document.getElementById('fileInput');
        const filesInput = document.getElementById('filesInput');
        if (fileInput) fileInput.value = '';
        if (filesInput) filesInput.value = '';
        this.displaySelectedFiles();
        this.showUploadSettings();

        // 重置所有设置界面
        document.getElementById('uploadSettings').style.display = 'none';
        document.getElementById('taskListSection').style.display = 'none';
        document.getElementById('uploadFolder').value = '';
        document.getElementById('taskListFile').value = '';
    }

    // URL映射处理 - 第一步：解析URL数据
    async parseUrls() {
        const urlInput = document.getElementById('urlInput');
        if (!urlInput) return;

        const urlText = urlInput.value.trim();
        if (!urlText) {
            this.showNotification('warning', '请输入URL列表');
            return;
        }

        this.showLoading(true);

        try {
            const lines = urlText.split('\n').filter(line => line.trim());
            if (lines.length === 0) {
                this.showLoading(false);
                this.showNotification('warning', '未检测到有效URL');
                return;
            }

            const urls = [];
            const invalidUrls = [];

            // 在客户端解析URL，提取ASIN和图片类型
            for (const line of lines) {
                try {
                    // 检查是否包含|分隔符
                    const parts = line.split('|');
                    if (parts.length === 2) {
                        // 使用filename|url格式
                        urls.push({
                            filename: parts[0].trim(),
                            url: parts[1].trim()
                        });
                        console.log(`添加 filename|url 格式: ${parts[0].trim()} | ${parts[1].trim()}`);
                    } else if (line.trim().startsWith('http')) {
                        // 直接使用URL，智能提取ASIN和图片类型
                        const url = line.trim();
                        let asin = null;
                        let imageType = "MAIN";

                        // 尝试从URL路径中提取ASIN
                        const urlParts = url.split('/');
                        const lastPart = urlParts[urlParts.length - 1];

                        // 检查最后部分是否包含B0开头的ASIN
                        const asinMatch = lastPart.match(/B0[A-Z0-9]{8}/);
                        if (asinMatch) {
                            asin = asinMatch[0];
                            console.log(`从URL提取到ASIN: ${asin}`);

                            // 提取图片类型 - 支持多种格式
                            if (lastPart.includes('_PT')) {
                                // 支持 _PT1, _PT01 等格式
                                const ptMatch = lastPart.match(/_PT(\d+)/);
                                if (ptMatch) {
                                    const ptNum = parseInt(ptMatch[1]);
                                    imageType = `PT${ptNum.toString().padStart(2, '0')}`;
                                    console.log(`从URL提取到图片类型: ${imageType}`);
                                }
                            } else if (lastPart.includes('.PT')) {
                                // 支持 .PT1, .PT01 等格式（如 B0XXXXXXXX.PT01.jpg）
                                const ptMatch = lastPart.match(/\.PT(\d+)/);
                                if (ptMatch) {
                                    const ptNum = parseInt(ptMatch[1]);
                                    imageType = `PT${ptNum.toString().padStart(2, '0')}`;
                                    console.log(`从URL提取到图片类型: ${imageType}`);
                                }
                            } else if (lastPart.includes('_MAIN') || lastPart.includes('.MAIN')) {
                                imageType = 'MAIN';
                            } else if (lastPart.includes('_SWATCH') || lastPart.includes('.SWATCH')) {
                                imageType = 'SWATCH';
                            } else {
                                // 如果没有明确的类型标识，尝试从文件名末尾推断
                                if (lastPart.match(/\.M\d*/)) {
                                    // 以.M开头的可能是MAIN图
                                    imageType = 'MAIN';
                                } else if (lastPart.match(/\.P\d+/)) {
                                    // 以.P开头的可能是PT图，尝试提取数字
                                    const pMatch = lastPart.match(/\.P(\d+)/);
                                    if (pMatch && pMatch[1]) {
                                        const ptNum = parseInt(pMatch[1]);
                                        if (ptNum >= 1 && ptNum <= 8) {
                                            imageType = `PT${ptNum.toString().padStart(2, '0')}`;
                                        } else {
                                            imageType = 'PT01'; // 默认为PT01
                                        }
                                    } else {
                                        imageType = 'PT01';
                                    }
                                    console.log(`从文件名格式推断图片类型: ${imageType}`);
                                } else if (lastPart.match(/\.S\d*/)) {
                                    // 以.S开头的可能是SWCH图
                                    imageType = 'SWCH';
                                } else {
                                    // 默认为MAIN
                                    imageType = 'MAIN';
                                }
                            }
                        } else {
                            // 从URL其他部分尝试提取ASIN
                            for (const part of urlParts) {
                                if (part.startsWith('B0') && part.length >= 10) {
                                    asin = part.substring(0, 10);
                                    console.log(`从URL路径部分提取到ASIN: ${asin}`);
                                    break;
                                }
                            }

                            // 如果仍然没有找到ASIN，从URL本身再次尝试
                            if (!asin) {
                                const fullUrlMatch = url.match(/B0[A-Z0-9]{8}/);
                                if (fullUrlMatch) {
                                    asin = fullUrlMatch[0];
                                    console.log(`从完整URL提取到ASIN: ${asin}`);
                                }
                            }

                            // 最后实在找不到，生成一个基于URL的唯一标识
                            if (!asin) {
                                const urlHash = this.generateUrlHash(url);
                                asin = `B0${urlHash}`;
                                console.log(`为URL生成哈希作为ASIN: ${asin}`);
                            }
                        }

                        // 构造规范的文件名
                        const fileName = `${asin}_${imageType}.jpg`;

                        urls.push({
                            filename: fileName,
                            url: url
                        });
                        console.log(`添加URL格式: ${url} -> ${fileName}`);
                    } else {
                        // 无效URL
                        invalidUrls.push(line);
                        console.log(`无效URL格式: ${line}`);
                    }
                } catch (lineError) {
                    console.error(`处理URL行时出错: ${line}`, lineError);
                    invalidUrls.push(line);
                }
            }

            console.log(`处理结果: ${urls.length}个有效, ${invalidUrls.length}个无效`);

            if (urls.length === 0) {
                this.showLoading(false);
                this.showNotification('warning', '没有有效的URL数据，请检查格式');
                return;
            }

            // 统计ASIN数量
            const asinSet = new Set();
            urls.forEach(item => {
                if (item.filename.includes('_')) {
                    const asin = item.filename.split('_')[0];
                    asinSet.add(asin);
                }
            });

            // 保存解析结果，供第二步使用
            this.parsedUrls = urls;
            
            // 显示解析结果
            this.showUrlParseResult(urls.length, asinSet.size, invalidUrls.length);
            
            this.showNotification('success', `URL解析完成！共解析 ${urls.length} 个URL，涉及 ${asinSet.size} 个ASIN`);

            // 如果有无效URL，显示警告
            if (invalidUrls.length > 0) {
                this.showNotification('warning', `有${invalidUrls.length}个URL格式无效，已跳过`);
            }

        } catch (error) {
            console.error('URL解析错误:', error);
            this.showNotification('error', `解析出错: ${error.message}`);
        } finally {
            this.showLoading(false);
        }
    }

    // 显示URL解析结果
    showUrlParseResult(urlCount, asinCount, invalidCount) {
        const urlParseSteps = document.getElementById('urlParseSteps');
        const parsedUrlCount = document.getElementById('parsedUrlCount');
        const parsedAsinCount = document.getElementById('parsedAsinCount');
        const taskListSection = document.getElementById('taskListSection');

        if (parsedUrlCount) parsedUrlCount.textContent = urlCount;
        if (parsedAsinCount) parsedAsinCount.textContent = asinCount;
        if (urlParseSteps) urlParseSteps.style.display = 'block';

        // 在解析模式下，显示任务列表选择
        if (this.currentMode === 'parse' && taskListSection) {
            taskListSection.style.display = 'block';
        }
    }

    // URL映射处理 - 第二步：生成Excel文件
    async generateUrlExcel() {
        if (!this.parsedUrls || this.parsedUrls.length === 0) {
            this.showNotification('error', '没有解析的URL数据，请先执行第一步');
            return;
        }

        this.showLoading(true);

        try {
            // 准备发送到服务器的数据
            const requestData = {
                urls: this.parsedUrls
            };

            // 添加任务列表文件（如果选择了）
            const taskListFile = document.getElementById('taskListFile').files[0];
            let response;

            if (taskListFile) {
                const formData = new FormData();
                formData.append('urls', JSON.stringify(this.parsedUrls));
                formData.append('task_list_file', taskListFile);
                console.log(`使用任务列表文件: ${taskListFile.name}`);

                // 发送请求到服务器生成Excel
                response = await fetch('/api/parse-urls', {
                    method: 'POST',
                    body: formData
                });
            } else {
                // 发送请求到服务器生成Excel
                response = await fetch('/api/parse-urls', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
            }

            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            console.log('服务器响应:', result);

            if (result.success) {
                this.showResults(result);
                this.showNotification('success', 'Excel映射表生成成功');

                // 隐藏解析步骤，显示结果
                const urlParseSteps = document.getElementById('urlParseSteps');
                if (urlParseSteps) urlParseSteps.style.display = 'none';
            } else {
                throw new Error(result.error || '服务器处理失败');
            }

        } catch (error) {
            console.error('生成Excel失败:', error);
            this.showNotification('error', `生成Excel失败: ${error.message}`);
        } finally {
            this.showLoading(false);
        }
    }



    clearUrls() {
        const urlInput = document.getElementById('urlInput');
        const urlParseSteps = document.getElementById('urlParseSteps');
        const resultCard = document.getElementById('resultCard');
        const taskListFile = document.getElementById('taskListFile');

        if (urlInput) urlInput.value = '';
        if (urlParseSteps) urlParseSteps.style.display = 'none';
        if (resultCard) resultCard.style.display = 'none';
        if (taskListFile) taskListFile.value = '';

        // 清理解析结果数据
        this.parsedUrls = null;
    }

    // 历史记录处理
    async loadHistoryFiles() {
        try {
            const response = await fetch('/api/history-files');
            const result = await response.json();

            const tbody = document.getElementById('historyTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';

            if (result.success && result.files.length > 0) {
                result.files.forEach(file => {
                    const row = this.createHistoryTableRow(file);
                    tbody.appendChild(row);
                });
            } else {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">暂无历史文件</td></tr>';
            }
        } catch (error) {
            console.error('加载历史文件失败:', error);
            const tbody = document.getElementById('historyTableBody');
            if (tbody) {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">加载失败</td></tr>';
            }
        }
    }

    createHistoryTableRow(file) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${file.filename}</td>
            <td>${this.formatFileSize(file.size)}</td>
            <td>${file.modified}</td>
            <td>
                <a href="/api/download-history/${file.filename}" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-download"></i> 下载
                </a>
            </td>
        `;
        return row;
    }

    // 系统测试处理
    async testApiConnection() {
        this.showLoading(true);

        try {
            const response = await fetch('/api/test-connection');
            const result = await response.json();

            this.showTestResults(result);
        } catch (error) {
            this.showTestResults({
                success: false,
                error: error.message
            });
        } finally {
            this.showLoading(false);
        }
    }

    showTestResults(result) {
        const testResultCard = document.getElementById('testResultCard');
        const testResults = document.getElementById('testResults');

        if (!testResultCard || !testResults) return;

        testResultCard.style.display = 'block';

        if (result.success) {
            testResults.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="bi bi-check-circle"></i> 连接成功</h6>
                    <pre class="mb-0 small">${JSON.stringify(result.result, null, 2)}</pre>
                </div>
            `;
            this.showNotification('success', 'API连接测试成功');
        } else {
            testResults.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="bi bi-x-circle"></i> 连接失败</h6>
                    <p class="mb-0">${result.error}</p>
                </div>
            `;
            this.showNotification('error', 'API连接测试失败');
        }
    }

    // Tab切换处理
    handleTabChange(event) {
        const targetTab = event.target.getAttribute('data-bs-target');
        console.log('[Tab调试] 切换到Tab:', targetTab);

        // 根据不同Tab执行相应的初始化操作
        switch (targetTab) {
            case '#history-tab-pane':
                console.log('[Tab调试] 初始化历史记录面板');
                this.loadHistoryFiles();
                break;
            case '#upload-tab-pane':
                console.log('[Tab调试] 初始化上传面板');
                // 可以在这里初始化上传相关功能
                break;
            case '#url-tab-pane':
                console.log('[Tab调试] 初始化URL解析面板');
                // URL解析面板初始化
                break;
            case '#template-tab-pane':
                console.log('[Tab调试] 初始化模板填充面板');
                // 模板填充面板初始化
                break;
            case '#rename-tab-pane':
                console.log('[Tab调试] 初始化图片重命名面板');
                // 图片重命名面板初始化
                break;
            default:
                console.log('[Tab调试] 未知Tab:', targetTab);
        }
    }

    // 手动Tab切换的备用方案
    manualTabSwitch(targetTabId) {
        console.log('[手动Tab] 切换到:', targetTabId);
        
        // 隐藏所有Tab内容
        const allTabPanes = document.querySelectorAll('.tab-pane');
        allTabPanes.forEach(pane => {
            pane.classList.remove('show', 'active');
        });
        
        // 移除所有Tab按钮的active状态
        const allTabButtons = document.querySelectorAll('.nav-link');
        allTabButtons.forEach(button => {
            button.classList.remove('active');
            button.setAttribute('aria-selected', 'false');
        });
        
        // 显示目标Tab内容
        const targetPane = document.querySelector(targetTabId);
        if (targetPane) {
            targetPane.classList.add('show', 'active');
        }
        
        // 激活对应的Tab按钮
        const targetButton = document.querySelector(`[data-bs-target="${targetTabId}"]`);
        if (targetButton) {
            targetButton.classList.add('active');
            targetButton.setAttribute('aria-selected', 'true');
        }
        
        // 触发Tab切换事件
        this.handleTabChange({ target: { getAttribute: () => targetTabId } });
    }

    // 工具函数
    showLoading(show) {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = show ? 'flex' : 'none';
        }
    }

    showNotification(type, message) {
        // 移除现有的通知（避免重叠）
        const existingNotifications = document.querySelectorAll('.custom-notification');
        existingNotifications.forEach(notification => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });

        // 简单的通知实现，可以替换为更复杂的通知组件
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const icon = {
            'success': 'bi-check-circle',
            'error': 'bi-x-circle',
            'warning': 'bi-exclamation-triangle',
            'info': 'bi-info-circle'
        }[type] || 'bi-info-circle';

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed custom-notification`;
        notification.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            z-index: 99999 !important;
            min-width: 300px !important;
            max-width: 400px !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
            border: 1px solid rgba(0,0,0,0.1) !important;
            margin: 0 !important;
            padding: 12px 16px !important;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        notification.innerHTML = `
            <i class="bi ${icon}"></i> ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;

        // 确保添加到body最后
        document.body.appendChild(notification);

        // 添加显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // 自动删除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 5000);

        // 调试信息
        console.log(`[通知调试] 显示${type}通知: ${message}`);
    }

    /**
     * 显示详细的错误信息，包含文件名和具体错误原因
     */
    showDetailedErrorMessage(message, invalidFiles) {
        let detailedMessage = message + '\n\n📋 详细错误信息:\n';

        if (invalidFiles && invalidFiles.length > 0) {
            invalidFiles.slice(0, 5).forEach((item, index) => {
                const fileName = item.file ? item.file.name : item.filename || '未知文件';
                const reason = item.reason || '未知错误';
                detailedMessage += `${index + 1}. ${fileName}\n   ❌ ${reason}\n`;
            });

            if (invalidFiles.length > 5) {
                detailedMessage += `\n... 还有 ${invalidFiles.length - 5} 个文件有类似问题\n`;
            }
        }

        detailedMessage += '\n💡 文件命名要求:\n';
        detailedMessage += '• 必须以B0开头（Amazon ASIN格式）\n';
        detailedMessage += '• 必须包含MAIN、PT或SWCH关键词\n';
        detailedMessage += '• 支持格式: PNG, JPG, JPEG, GIF, BMP, WEBP\n';
        detailedMessage += '\n✅ 正确示例:\n';
        detailedMessage += '• B0ABCDEFGH_MAIN.jpg (主图)\n';
        detailedMessage += '• B0ABCDEFGH_PT01.jpg (附图)\n';
        detailedMessage += '• B0ABCDEFGH_SWCH.jpg (色块图)';

        // 使用更友好的模态框显示错误信息
        this.showErrorModal('文件验证失败', detailedMessage);
    }

    /**
     * 显示错误模态框
     */
    showErrorModal(title, message) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="errorDetailModal" tabindex="-1" aria-labelledby="errorDetailModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title" id="errorDetailModalLabel">
                                <i class="bi bi-exclamation-triangle"></i> ${title}
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                        </div>
                        <div class="modal-body">
                            <pre style="white-space: pre-wrap; font-family: inherit; margin: 0;">${message}</pre>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除现有的错误模态框
        const existingModal = document.getElementById('errorDetailModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新的模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = document.getElementById('errorDetailModal');
        if (typeof bootstrap !== 'undefined' && !window.bootstrapFallbackMode) {
            try {
                const modalInstance = new bootstrap.Modal(modal);
                modalInstance.show();

                // 监听关闭事件，清理DOM
                modal.addEventListener('hidden.bs.modal', function () {
                    modal.remove();
                });
            } catch (error) {
                console.error('❌ Bootstrap模态框创建失败，使用Toast通知:', error);
                this.showNotification('info', message);
                modal.remove();
            }
        } else {
            // 备用方案：使用Toast通知
            this.showNotification('info', message);
            modal.remove();
        }
    }

    // 生成URL的哈希值
    generateUrlHash(url) {
        // 简单的哈希函数，生成8位字符
        let hash = 0;
        for (let i = 0; i < url.length; i++) {
            const char = url.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        // 转换为正数并格式化为8位十六进制
        const positiveHash = Math.abs(hash).toString(16).padStart(8, '0').toUpperCase();
        return positiveHash.substring(0, 8);
    }

    // 模板填充相关方法
    validateTemplateFiles() {
        const templateFile = document.getElementById('templateFile');
        const reportFile = document.getElementById('reportFile');
        const mappingFile = document.getElementById('mappingFile');
        const fillTemplateBtn = document.getElementById('fillTemplateBtn');

        // 检查必需文件是否都已选择
        const hasTemplateFile = templateFile && templateFile.files.length > 0;
        const hasReportFile = reportFile && reportFile.files.length > 0;
        const hasMappingFile = mappingFile && mappingFile.files.length > 0;

        // 只有三个必需文件都选择了才启用按钮
        if (fillTemplateBtn) {
            fillTemplateBtn.disabled = !(hasTemplateFile && hasReportFile && hasMappingFile);
        }

        console.log('[模板填充] 文件验证:', {
            template: hasTemplateFile,
            report: hasReportFile,
            mapping: hasMappingFile,
            buttonEnabled: !fillTemplateBtn?.disabled
        });
    }

    clearTemplateFiles() {
        // 清空所有文件选择
        const fileInputs = ['templateFile', 'reportFile', 'mappingFile', 'productInfoFile'];
        fileInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.value = '';
            }
        });

        // 禁用填充按钮
        const fillTemplateBtn = document.getElementById('fillTemplateBtn');
        if (fillTemplateBtn) {
            fillTemplateBtn.disabled = true;
        }

        // 隐藏结果卡片
        const templateResultCard = document.getElementById('templateResultCard');
        if (templateResultCard) {
            templateResultCard.style.display = 'none';
        }

        console.log('[模板填充] 已清空所有文件选择');
        this.showNotification('info', '已清空所有文件选择');
    }

    handleUpdateModeChange() {
        const updateModeElement = document.querySelector('input[name="updateMode"]:checked');
        const reportFileRequired = document.getElementById('reportFileRequired');
        const reportFileOptional = document.getElementById('reportFileOptional');

        if (updateModeElement) {
            const updateMode = updateModeElement.value;

            if (updateMode === 'full') {
                // 全部更新：商品分类报告必需
                if (reportFileRequired) reportFileRequired.style.display = 'inline';
                if (reportFileOptional) reportFileOptional.style.display = 'none';
            } else {
                // 部分更新：商品分类报告可选
                if (reportFileRequired) reportFileRequired.style.display = 'none';
                if (reportFileOptional) reportFileOptional.style.display = 'inline';
            }

            // 重新验证文件
            this.validateTemplateFiles();
        }
    }

    async handleFillTemplate() {
        try {
            console.log('[模板填充] 开始处理');

            // 获取更新方式
            const updateModeElement = document.querySelector('input[name="updateMode"]:checked');
            if (!updateModeElement) {
                this.showNotification('error', '请选择更新方式（部分更新或全部更新）');
                return;
            }
            const updateMode = updateModeElement.value;

            // 安全获取文件输入元素
            const templateFileElement = document.getElementById('templateFile');
            const reportFileElement = document.getElementById('reportFile');
            const mappingFileElement = document.getElementById('mappingFile');
            const productInfoFileElement = document.getElementById('productInfoFile');

            // 检查元素是否存在
            if (!templateFileElement || !mappingFileElement) {
                console.error('[模板填充] 找不到文件输入元素');
                this.showNotification('error', '页面元素加载异常，请刷新页面重试');
                return;
            }

            // 获取文件
            const templateFile = templateFileElement.files[0];
            const reportFile = reportFileElement ? reportFileElement.files[0] : null;
            const mappingFile = mappingFileElement.files[0];
            const productInfoFile = productInfoFileElement ? productInfoFileElement.files[0] : null;

            // 验证必需文件
            if (!templateFile || !mappingFile) {
                this.showNotification('error', '请选择必需文件（亚马逊模板、图片映射）');
                return;
            }

            // 验证全部更新时的商品分类报告
            if (updateMode === 'full' && !reportFile) {
                this.showNotification('error', '全部更新模式需要上传商品分类报告文件');
                return;
            }

            console.log('[模板填充] 文件验证通过:', {
                updateMode: updateMode,
                template: templateFile.name,
                report: reportFile ? reportFile.name : '未选择',
                mapping: mappingFile.name,
                productInfo: productInfoFile ? productInfoFile.name : '未选择'
            });

            // 获取设置选项
            const marketSelect = document.getElementById('marketSelect');
            const useProductInfo = document.getElementById('useProductInfo');

            // 创建FormData
            const formData = new FormData();
            formData.append('template_file', templateFile);
            formData.append('mapping_file', mappingFile);
            formData.append('update_mode', updateMode);

            // 根据更新方式添加商品分类报告
            if (updateMode === 'full' && reportFile) {
                formData.append('report_file', reportFile);
            }

            if (productInfoFile) {
                formData.append('product_info_file', productInfoFile);
            }

            // 添加设置参数
            formData.append('market', marketSelect ? marketSelect.value : 'US');
            formData.append('use_product_info', useProductInfo ? useProductInfo.checked : true);

            // 显示加载状态
            this.showLoading(true);
            const fillTemplateBtn = document.getElementById('fillTemplateBtn');
            if (fillTemplateBtn) {
                fillTemplateBtn.innerHTML = '<i class="bi bi-gear-fill spinning"></i> 处理中...';
                fillTemplateBtn.disabled = true;
            }

            console.log('[模板填充] 发送请求到服务器...');

            // 发送请求
            const response = await fetch('/api/fill-template', {
                method: 'POST',
                body: formData
            });

            console.log('[模板填充] 服务器响应状态:', response.status);

            if (!response.ok) {
                throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            console.log('[模板填充] 服务器响应数据:', result);

            if (result.success) {
                this.showTemplateResults(result);
                this.showNotification('success', `模板填充完成！${result.message || ''}`);
            } else {
                this.showNotification('error', `模板填充失败: ${result.error}`);
            }

        } catch (error) {
            console.error('[模板填充] 处理失败:', error);
            this.showNotification('error', `处理失败: ${error.message}`);
        } finally {
            // 恢复按钮状态
            this.showLoading(false);
            const fillTemplateBtn = document.getElementById('fillTemplateBtn');
            if (fillTemplateBtn) {
                fillTemplateBtn.innerHTML = '<i class="bi bi-gear"></i> 开始填充模板';
                fillTemplateBtn.disabled = false;
            }
        }
    }

    showTemplateResults(result) {
        const templateResultCard = document.getElementById('templateResultCard');
        const templateResults = document.getElementById('templateResults');
        const templateDownloadSection = document.getElementById('templateDownloadSection');

        if (!templateResultCard || !templateResults) {
            console.error('[模板填充] 找不到结果显示元素');
            return;
        }

        // 构建结果HTML
        let resultHtml = '<div class="list-group list-group-flush">';

        if (result.files && result.files.length > 0) {
            result.files.forEach(file => {
                resultHtml += `
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">${file.output_name}</h6>
                            <small class="text-success">
                                <i class="bi bi-check-circle"></i> 完成
                            </small>
                        </div>
                        <p class="mb-1 text-muted">来源: ${file.original_name}</p>
                        ${file.sheets ? `<small>工作表: ${file.sheets.join(', ')}</small>` : ''}
                        <div class="mt-2">
                            <a href="${file.download_url}" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="bi bi-download"></i> 下载文件
                            </a>
                        </div>
                    </div>
                `;
            });
        } else if (result.result_file) {
            // 处理单个文件结果（向后兼容）
            resultHtml += `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${result.result_file}</h6>
                        <small class="text-success">
                            <i class="bi bi-check-circle"></i> 完成
                        </small>
                    </div>
                    <p class="mb-1 text-muted">模板填充完成</p>
                    <div class="mt-2">
                        <a href="${result.download_url}" class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="bi bi-download"></i> 下载文件
                        </a>
                    </div>
                </div>
            `;
        }

        if (result.image_results && result.image_results.length > 0) {
            const successCount = result.image_results.filter(r => r.success).length;
            resultHtml += `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">图片上传结果</h6>
                        <small class="text-info">
                            <i class="bi bi-images"></i> ${successCount}/${result.image_results.length}
                        </small>
                    </div>
                    <p class="mb-1 text-muted">成功上传 ${successCount} 张图片</p>
                </div>
            `;
        }

        resultHtml += '</div>';
        templateResults.innerHTML = resultHtml;

        // 显示结果卡片和下载区域
        templateResultCard.style.display = 'block';
        if (templateDownloadSection && result.files && result.files.length > 0) {
            templateDownloadSection.style.display = 'block';
            
            // 设置下载按钮事件（下载最新的文件）
            const downloadBtn = document.getElementById('downloadTemplateResult');
            if (downloadBtn) {
                const latestFile = result.files[result.files.length - 1];
                downloadBtn.onclick = () => {
                    window.open(`/api/download/${latestFile.output_name}`, '_blank');
                };
            }
        }

        // 刷新历史文件列表
        this.loadHistoryFiles();
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', function () {
    window.amazonUploader = new AmazonImageUploader();
});

// 全局错误处理
window.addEventListener('error', function (event) {
    console.error('全局错误:', event.error);
});

// 全局未处理的Promise拒绝
window.addEventListener('unhandledrejection', function (event) {
    console.error('未处理的Promise拒绝:', event.reason);
    event.preventDefault();
});

// 图片重命名功能 - 使用原生JavaScript和jQuery混合方式
$(document).ready(function() {
    console.log('📝 初始化图片重命名功能');
    
    // 重命名表单提交处理
    $('#renameForm').on('submit', function(e) {
        console.log('🚀 重命名表单提交事件触发');
        e.preventDefault();
        e.stopPropagation();
        
        // 验证表单
        const excelFile = $('#rename_excel_file')[0].files[0];
        if (!excelFile) {
            $('#rename_excel_file').addClass('is-invalid');
            return false;
        }
        
        const imageFiles = $('#rename_image_folder')[0].files;
        if (imageFiles.length === 0) {
            $('#rename_image_folder').addClass('is-invalid');
            return false;
        }
        
        // 验证至少选择了一个处理选项
        const mainChecked = $('#process_main').prop('checked');
        const sceneChecked = $('#process_scene').prop('checked');
        const swatchChecked = $('#process_swatch').prop('checked');
        
        if (!mainChecked && !sceneChecked && !swatchChecked) {
            $('#rename-error').text('请至少选择一个处理选项').show();
            return false;
        }
        
        // 显示加载状态
        showLoading(true);
        
        // 准备表单数据
        const formData = new FormData(this);
        
        // 发送请求
        $.ajax({
            url: '/api/rename-images',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                xhr.responseType = 'blob';
                return xhr;
            },
            success: function(blob, status, xhr) {
                hideLoading();
                
                // 获取文件名
                const contentDisposition = xhr.getResponseHeader('content-disposition');
                let filename = 'renamed_images.zip';
                if (contentDisposition && contentDisposition.indexOf('filename=') !== -1) {
                    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    const matches = filenameRegex.exec(contentDisposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['"]/g, '');
                    }
                }
                
                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                // 显示成功提示
                showToast('处理成功', '图片重命名处理完成，已下载ZIP文件', 'success');
            },
            error: function(xhr) {
                hideLoading();
                
                // 显示错误信息
                try {
                    const response = JSON.parse(xhr.responseText);
                    $('#rename-error').text(response.error || '处理失败').show();
                } catch (e) {
                    $('#rename-error').text('处理失败，请检查文件和选项').show();
                }
            }
        });
    });
    
    // 测试颜色匹配按钮点击处理
    $('#testColorMatchBtn').on('click', function() {
        // 验证表单
        const excelFile = $('#rename_excel_file')[0].files[0];
        if (!excelFile) {
            $('#rename_excel_file').addClass('is-invalid');
            return false;
        }
        
        const imageFiles = $('#rename_image_folder')[0].files;
        if (imageFiles.length === 0) {
            $('#rename_image_folder').addClass('is-invalid');
            return false;
        }
        
        // 显示加载状态
        showLoading(true);
        
        // 准备表单数据
        const formData = new FormData();
        formData.append('excel_file', excelFile);
        
        // 添加所有图片文件
        for (let i = 0; i < imageFiles.length; i++) {
            formData.append('image_folder', imageFiles[i]);
        }
        
        // 发送请求
        $.ajax({
            url: '/api/test-color-matching',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                hideLoading();
                
                // 显示结果区域
                $('#rename-results-section').show();
                
                // 处理新的响应格式
                if (response.status === 'error') {
                    $('#color-match-results').html(`
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            ${response.message}
                        </div>
                    `);
                    return;
                }
                
                if (response.status === 'warning') {
                    $('#color-match-results').html(`
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            ${response.message}
                        </div>
                    `);
                    return;
                }
                
                // 成功状态 - 显示友好的报告
                let alertClass = 'success';
                if (response.summary && response.summary.match_rate < 70) {
                    alertClass = 'warning';
                } else if (response.summary && response.summary.match_rate < 90) {
                    alertClass = 'info';
                }
                
                // 构建简洁明了的结果HTML
                let html = `
                    <div class="alert alert-${alertClass}">
                        <div style="white-space: pre-line; font-family: monospace; font-size: 14px;">
                            ${response.message}
                        </div>
                    </div>
                `;
                
                // 如果有统计信息，添加简要的数据卡片
                if (response.summary) {
                    const summary = response.summary;
                    const matchRate = summary.match_rate;
                    let progressBarClass = 'bg-danger';
                    if (matchRate >= 90) progressBarClass = 'bg-success';
                    else if (matchRate >= 70) progressBarClass = 'bg-warning';
                    
                    html += `
                        <div class="card mt-3">
                            <div class="card-body">
                                <h6 class="card-title">📊 快速统计</h6>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-2">
                                            <small class="text-muted">匹配成功率</small>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar ${progressBarClass}" role="progressbar" 
                                                    style="width: ${matchRate}%;" 
                                                    aria-valuenow="${matchRate}" 
                                                    aria-valuemin="0" aria-valuemax="100">
                                                    ${matchRate}%
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="text-center">
                                            <div class="h5 mb-0">${summary.matched_count}/${summary.total_excel_colors}</div>
                                            <small class="text-muted">匹配成功</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }
                
                $('#color-match-results').html(html);
            },
            error: function(xhr) {
                hideLoading();
                
                // 显示错误信息
                try {
                    const response = JSON.parse(xhr.responseText);
                    $('#rename-error').text(response.error || '颜色匹配测试失败').show();
                } catch (e) {
                    $('#rename-error').text('颜色匹配测试失败').show();
                }
                
                // 隐藏结果区域
                $('#rename-results-section').hide();
            }
        });
    });
    
    // 表单字段验证重置
    $('#rename_excel_file').on('change', function() {
        $(this).removeClass('is-invalid');
    });
    
    $('#rename_image_folder').on('change', function() {
        $(this).removeClass('is-invalid');
    });
    
    $('input[name="process_main"], input[name="process_scene"], input[name="process_swatch"]').on('change', function() {
        $('#rename-error').hide();
    });
});

// 辅助函数
function hideLoading() {
    $('#loadingOverlay').hide();
}

function showToast(title, message, type = 'success') {
    // 创建并显示Toast通知
    const toastHtml = `
        <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${title}</strong><br>${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;
    
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '1055';
        document.body.appendChild(toastContainer);
    }
    
    // 添加toast并显示
    const toastElement = document.createElement('div');
    toastElement.innerHTML = toastHtml;
    toastContainer.appendChild(toastElement.firstElementChild);
    
    // 显示toast
    const toast = new bootstrap.Toast(toastContainer.lastElementChild, {
        autohide: true,
        delay: 5000
    });
    toast.show();
    
    // 自动清理
    setTimeout(() => {
        if (toastContainer.lastElementChild) {
            toastContainer.removeChild(toastContainer.lastElementChild);
        }
    }, 6000);
}

// 文件夹管理功能
class FolderManager {
    constructor() {
        this.currentTaskId = null;
        this.progressInterval = null;
        this.bindEvents();
    }

    bindEvents() {
        // 文件夹Excel文件上传
        const folderExcelFile = document.getElementById('folderExcelFile');
        const folderExcelUploadArea = document.getElementById('folderExcelUploadArea');

        if (folderExcelFile && folderExcelUploadArea) {
            folderExcelUploadArea.addEventListener('click', () => {
                folderExcelFile.click();
            });

            folderExcelFile.addEventListener('change', (e) => {
                this.handleExcelFileSelect(e.target.files[0]);
            });

            // 拖拽上传
            folderExcelUploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                folderExcelUploadArea.classList.add('dragover');
            });

            folderExcelUploadArea.addEventListener('dragleave', () => {
                folderExcelUploadArea.classList.remove('dragover');
            });

            folderExcelUploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                folderExcelUploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleExcelFileSelect(files[0]);
                }
            });
        }
    }

    handleExcelFileSelect(file) {
        console.log('📄 处理Excel文件选择:', file);

        if (!file) {
            console.warn('⚠️ 没有选择文件');
            return;
        }

        // 验证文件类型
        const allowedTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'application/vnd.ms-excel.sheet.macroEnabled.12'
        ];

        console.log('🔍 文件类型:', file.type);
        console.log('🔍 文件名:', file.name);

        if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls|xlsm)$/i)) {
            showToast('error', '请选择有效的Excel文件（.xlsx, .xls, .xlsm）❌');
            return;
        }

        // 显示文件信息
        const uploadArea = document.getElementById('folderExcelUploadArea');
        if (!uploadArea) {
            console.error('❌ 找不到上传区域');
            showToast('error', '界面元素未找到，请刷新页面重试');
            return;
        }

        // 检查是否有新的HTML结构
        let placeholder = uploadArea.querySelector('.upload-placeholder');
        let success = uploadArea.querySelector('.upload-success');

        if (!placeholder || !success) {
            console.log('🔄 使用旧版HTML结构，动态创建状态元素');

            // 保存原始内容
            const originalContent = uploadArea.innerHTML;

            // 创建新的HTML结构
            uploadArea.innerHTML = `
                <div class="upload-placeholder">
                    ${originalContent}
                </div>
                <div class="upload-success" style="display: none;">
                    <i class="bi bi-check-circle text-success fs-1 mb-2"></i>
                    <p class="mb-2">文件已选择：<span class="filename fw-bold">${file.name}</span></p>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFolderExcelFile()">
                        <i class="bi bi-x"></i> 重新选择
                    </button>
                </div>
            `;

            // 重新获取元素
            placeholder = uploadArea.querySelector('.upload-placeholder');
            success = uploadArea.querySelector('.upload-success');
        }

        const filename = success.querySelector('.filename');
        if (filename) {
            filename.textContent = file.name;
        }

        // 更新界面
        if (placeholder) placeholder.style.display = 'none';
        if (success) success.style.display = 'block';

        // 存储文件
        this.selectedExcelFile = file;

        console.log('✅ Excel文件选择成功:', file.name);
        showToast('success', `Excel文件已选择：${file.name} ✅`);
    }

    async startExport() {
        try {
            // 验证输入
            if (!this.selectedExcelFile) {
                showToast('error', '请选择SKU列表Excel文件');
                return;
            }

            const sourcePath = document.getElementById('folderSourcePath').value.trim();
            const exportPath = document.getElementById('folderExportPath').value.trim();

            if (!sourcePath || !exportPath) {
                showToast('error', '请选择源文件夹路径和导出目标路径');
                return;
            }

            // 准备表单数据
            const formData = new FormData();
            formData.append('excel_file', this.selectedExcelFile);
            formData.append('source_path', sourcePath);
            formData.append('export_path', exportPath);

            // 显示进度区域
            this.showProgressCard();

            // 发送请求
            const response = await fetch('/api/folder/export', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.currentTaskId = result.task_id;
                showToast('success', '文件夹导出任务已启动');
                this.startProgressMonitoring();
            } else {
                showToast('error', result.error || '启动导出任务失败');
                this.hideProgressCard();
            }

        } catch (error) {
            console.error('文件夹导出错误:', error);
            showToast('error', '导出请求失败: ' + error.message);
            this.hideProgressCard();
        }
    }

    showProgressCard() {
        const progressCard = document.getElementById('folderProgressCard');
        const resultCard = document.getElementById('folderResultCard');

        if (progressCard) progressCard.style.display = 'block';
        if (resultCard) resultCard.style.display = 'none';

        // 重置进度
        this.updateProgress(0, 0, 0, 0, '准备中...');
    }

    hideProgressCard() {
        const progressCard = document.getElementById('folderProgressCard');
        if (progressCard) progressCard.style.display = 'none';
    }

    updateProgress(processed, total, success, failed, status) {
        const progressBar = document.getElementById('folderProgressBar');
        const progressText = document.getElementById('folderProgressText');
        const currentStatus = document.getElementById('folderCurrentStatus');
        const successCount = document.getElementById('folderSuccessCount');
        const failedCount = document.getElementById('folderFailedCount');

        const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.textContent = percentage + '%';
        }

        if (progressText) progressText.textContent = `${processed}/${total}`;
        if (currentStatus) currentStatus.textContent = status;
        if (successCount) successCount.textContent = success;
        if (failedCount) failedCount.textContent = failed;
    }

    startProgressMonitoring() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        this.progressInterval = setInterval(async () => {
            if (this.currentTaskId) {
                await this.checkProgress();
            }
        }, 1000);
    }

    async checkProgress() {
        try {
            const response = await fetch(`/api/folder/progress/${this.currentTaskId}`);
            const result = await response.json();

            if (result.success) {
                const taskInfo = result.task_info;

                this.updateProgress(
                    taskInfo.processed_items || 0,
                    taskInfo.total_items || 0,
                    taskInfo.success_count || 0,
                    taskInfo.failed_count || 0,
                    taskInfo.current_item || '处理中...'
                );

                // 检查是否完成
                if (taskInfo.status === 'completed' || taskInfo.status === 'failed') {
                    clearInterval(this.progressInterval);
                    this.showResult(taskInfo);
                }
            }
        } catch (error) {
            console.error('检查进度失败:', error);
        }
    }

    showResult(taskInfo) {
        const progressCard = document.getElementById('folderProgressCard');
        const resultCard = document.getElementById('folderResultCard');
        const results = document.getElementById('folderResults');
        const actionSection = document.getElementById('folderActionSection');

        // 隐藏进度，显示结果
        if (progressCard) progressCard.style.display = 'none';
        if (resultCard) resultCard.style.display = 'block';

        // 显示结果信息
        if (results) {
            const totalTime = taskInfo.total_time ? `${taskInfo.total_time.toFixed(2)}秒` : '未知';
            results.innerHTML = `
                <div class="alert alert-${taskInfo.status === 'completed' ? 'success' : 'danger'} border-0">
                    <h6><i class="bi bi-${taskInfo.status === 'completed' ? 'check-circle' : 'x-circle'}"></i>
                        ${taskInfo.status === 'completed' ? '导出完成' : '导出失败'}</h6>
                    <p class="mb-1"><strong>成功：</strong>${taskInfo.success_count || 0} 个文件夹</p>
                    <p class="mb-1"><strong>失败：</strong>${taskInfo.failed_count || 0} 个文件夹</p>
                    <p class="mb-1"><strong>总耗时：</strong>${totalTime}</p>
                    ${taskInfo.export_path ? `<p class="mb-1"><strong>导出位置：</strong><br><small>${taskInfo.export_path}</small></p>` : ''}
                </div>
            `;
        }

        // 显示操作按钮
        if (actionSection && taskInfo.status === 'completed') {
            actionSection.style.display = 'block';

            // 设置工作流路径
            if (taskInfo.export_path && taskInfo.source_excel_path) {
                this.setWorkflowPaths(taskInfo.export_path, taskInfo.source_excel_path);
            }

            // 检查是否选择了继续处理流程
            const continueProcessing = document.getElementById('folderContinueProcessing');
            if (continueProcessing && continueProcessing.checked) {
                // 延迟3秒后自动进入第二步
                setTimeout(() => {
                    if (typeof showProcessingOptions === 'function') {
                        showProcessingOptions();
                    }
                }, 3000);
            }
        }
    }

    async setWorkflowPaths(exportPath, sourceExcelPath) {
        try {
            await fetch('/api/folder/set-workflow-paths', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    export_path: exportPath,
                    source_excel_path: sourceExcelPath
                })
            });

            this.workflowPaths = {
                export_path: exportPath,
                source_excel_path: sourceExcelPath
            };

        } catch (error) {
            console.error('设置工作流路径失败:', error);
        }
    }

    continueImageProcessing() {
        // 显示处理方式选择界面
        this.showProcessingMethodSelection();
    }

    showProcessingMethodSelection() {
        // 创建模态框显示处理方式选择
        const modalHtml = `
            <div class="modal fade" id="processingMethodModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">选择图片处理方式</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="card h-100 border-warning">
                                        <div class="card-body text-center">
                                            <i class="bi bi-tools display-4 text-warning mb-3"></i>
                                            <h5>方式一：图片重命名工具</h5>
                                            <p class="text-muted">生成规范命名的压缩包，直接上传亚马逊</p>
                                            <button class="btn btn-warning" onclick="folderManager.selectMethod('rename')">
                                                选择此方式
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card h-100 border-primary">
                                        <div class="card-body text-center">
                                            <i class="bi bi-cloud-upload display-4 text-primary mb-3"></i>
                                            <h5>方式二：亚马逊模板填充</h5>
                                            <p class="text-muted">图片上传 + URL映射 + 模板填充</p>
                                            <button class="btn btn-primary" onclick="folderManager.selectMethod('template')">
                                                选择此方式
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('processingMethodModal'));
        modal.show();

        // 模态框关闭时清理
        document.getElementById('processingMethodModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    selectMethod(method) {
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('processingMethodModal'));
        modal.hide();

        if (method === 'rename') {
            // 切换到图片重命名选项卡
            this.switchToTab('rename-tab');
            showToast('info', '已切换到图片重命名工具，路径已自动设置');
        } else if (method === 'template') {
            // 切换到图片链接管理选项卡
            this.switchToTab('upload-tab');
            showToast('info', '已切换到图片链接管理，路径已自动设置');
        }
    }

    switchToTab(tabId) {
        const tab = document.getElementById(tabId);
        if (tab) {
            tab.click();
        }
    }
}

// 全局函数
function clearFolderExcelFile() {
    console.log('🗑️ 清除Excel文件选择');

    const uploadArea = document.getElementById('folderExcelUploadArea');
    if (!uploadArea) {
        console.error('❌ 找不到上传区域');
        return;
    }

    const placeholder = uploadArea.querySelector('.upload-placeholder');
    const success = uploadArea.querySelector('.upload-success');
    const fileInput = document.getElementById('folderExcelFile');

    if (placeholder && success) {
        // 如果有新结构，显示placeholder，隐藏success
        placeholder.style.display = 'block';
        success.style.display = 'none';
    } else {
        // 如果是旧结构，恢复原始内容
        uploadArea.innerHTML = `
            <i class="bi bi-cloud-upload fs-1 text-success mb-2"></i>
            <p class="mb-2">点击选择或拖拽Excel文件</p>
            <small class="text-muted">支持 .xlsx, .xls, .xlsm 格式，需包含SKU列</small>
            <input type="file" id="folderExcelFile" accept=".xlsx,.xls,.xlsm" style="display: none;">
        `;

        // 重新绑定事件
        const newFileInput = document.getElementById('folderExcelFile');
        const newUploadArea = document.getElementById('folderExcelUploadArea');

        if (newFileInput && newUploadArea && window.folderManager) {
            newUploadArea.addEventListener('click', () => {
                newFileInput.click();
            });

            newFileInput.addEventListener('change', (e) => {
                window.folderManager.handleExcelFileSelect(e.target.files[0]);
            });
        }
    }

    if (fileInput) {
        fileInput.value = '';
    }

    // 清除存储的文件
    if (window.folderManager) {
        window.folderManager.selectedExcelFile = null;
    }

    showToast('info', '已清除文件选择，请重新选择Excel文件 📄');
}

/**
 * 文件夹路径选择函数
 * @param {string} inputId - 输入框的ID
 */
function selectFolderPath(inputId) {
    console.log(`📁 选择文件夹路径: ${inputId}`);

    const textInput = document.getElementById(inputId);
    if (!textInput) {
        console.error(`❌ 找不到输入框: ${inputId}`);
        showToast('error', '输入框未找到，请刷新页面重试');
        return;
    }

    // 直接尝试使用浏览器文件夹选择器
    selectFolderWithBrowserDirect(inputId, textInput);
}

/**
 * 直接使用浏览器选择文件夹（带备用方案）
 * @param {string} inputId - 输入框ID
 * @param {HTMLElement} textInput - 输入框元素
 */
function selectFolderWithBrowserDirect(inputId, textInput) {
    console.log(`📁 直接使用浏览器选择文件夹: ${inputId}`);

    // 创建文件夹选择器
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.webkitdirectory = true;
    fileInput.directory = true;
    fileInput.multiple = true;
    fileInput.style.position = 'absolute';
    fileInput.style.left = '-9999px';
    fileInput.style.opacity = '0';
    fileInput.style.pointerEvents = 'none';

    // 监听文件选择事件
    fileInput.addEventListener('change', function(event) {
        const files = event.target.files;
        if (files && files.length > 0) {
            // 获取选择的文件夹路径
            const firstFile = files[0];
            let folderPath = firstFile.webkitRelativePath;

            // 提取文件夹路径（去掉文件名部分）
            const pathParts = folderPath.split('/');
            if (pathParts.length > 1) {
                pathParts.pop(); // 移除文件名
                folderPath = pathParts.join('/');
            }

            // 由于浏览器安全限制，只能获取相对路径，需要用户手动补充完整路径
            console.log(`📁 浏览器选择的相对路径: ${folderPath}`);

            // 显示路径输入对话框，让用户确认或修改为完整路径
            setTimeout(() => {
                const fullPath = prompt(
                    `📁 已选择文件夹: ${folderPath}\n\n` +
                    `⚠️ 浏览器只能显示相对路径，请输入完整的文件夹路径：\n` +
                    `例如：D:\\图片文件夹\\${folderPath}`,
                    folderPath // 默认值为相对路径
                );

                if (fullPath !== null && fullPath.trim() !== '') {
                    textInput.value = fullPath.trim();
                    showToast('success', `已设置文件夹路径: ${fullPath.trim()} 📁`);
                    console.log(`✅ 用户确认的完整路径: ${fullPath.trim()}`);
                } else {
                    // 用户取消或输入为空，使用相对路径
                    textInput.value = folderPath;
                    showToast('warning', `使用相对路径: ${folderPath} ⚠️\n建议手动修改为完整路径`);
                    console.log(`⚠️ 使用相对路径: ${folderPath}`);
                }
            }, 100);
        }

        // 清理临时元素
        if (fileInput.parentNode) {
            fileInput.parentNode.removeChild(fileInput);
        }
    });

    // 监听取消事件（用户没有选择文件夹）
    fileInput.addEventListener('cancel', function() {
        console.log('用户取消了文件夹选择');
        // 提供手动输入选项
        setTimeout(() => {
            const useManual = confirm('是否改为手动输入文件夹路径？\n\n确定 = 手动输入\n取消 = 重新选择');
            if (useManual) {
                selectFolderManually(inputId);
            }
        }, 100);

        // 清理临时元素
        if (fileInput.parentNode) {
            fileInput.parentNode.removeChild(fileInput);
        }
    });

    // 添加到DOM并触发点击
    document.body.appendChild(fileInput);

    // 立即触发点击
    try {
        fileInput.click();
        console.log('文件夹选择器已触发');
    } catch (error) {
        console.error('文件夹选择器触发失败:', error);
        // 如果失败，提供手动输入选项
        showToast('warning', '文件夹选择器启动失败，将使用手动输入方式');
        selectFolderManually(inputId);

        // 清理临时元素
        if (fileInput.parentNode) {
            fileInput.parentNode.removeChild(fileInput);
        }
    }
}

/**
 * 使用浏览器选择文件夹
 * @param {string} inputId - 输入框ID
 */
function selectFolderWithBrowser(inputId) {
    console.log(`📁 使用浏览器选择文件夹: ${inputId}`);

    const textInput = document.getElementById(inputId);
    if (!textInput) {
        console.error(`❌ 找不到输入框: ${inputId}`);
        showToast('error', '输入框未找到，请刷新页面重试');
        return;
    }

    // 立即创建并触发文件选择器，确保在用户激活的上下文中
    try {
        // 使用浏览器的文件夹选择器
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.webkitdirectory = true;
        fileInput.directory = true;
        fileInput.multiple = true;
        fileInput.style.position = 'absolute';
        fileInput.style.left = '-9999px';
        fileInput.style.opacity = '0';

        // 监听文件选择事件
        fileInput.addEventListener('change', function(event) {
            const files = event.target.files;
            if (files && files.length > 0) {
                // 获取选择的文件夹路径
                const firstFile = files[0];
                let folderPath = firstFile.webkitRelativePath;

                // 提取文件夹路径（去掉文件名部分）
                const pathParts = folderPath.split('/');
                if (pathParts.length > 1) {
                    pathParts.pop(); // 移除文件名
                    folderPath = pathParts.join('/');
                }

                // 显示相对路径，并提示用户确认
                textInput.value = folderPath;
                showToast('success', `已选择文件夹: ${folderPath} 📁\n⚠️ 注意：浏览器只能显示相对路径，请确认路径正确或手动修改为完整路径`);

                console.log(`✅ 已选择文件夹: ${folderPath}`);
            }

            // 清理临时元素
            if (fileInput.parentNode) {
                fileInput.parentNode.removeChild(fileInput);
            }
        });

        // 添加到DOM并立即触发点击
        document.body.appendChild(fileInput);

        // 使用setTimeout确保元素已添加到DOM
        setTimeout(() => {
            fileInput.click();
        }, 10);

    } catch (error) {
        console.error('文件夹选择器创建失败:', error);
        showToast('error', '文件夹选择器启动失败，请使用手动输入方式');
        selectFolderManually(inputId);
    }
}

/**
 * 手动输入文件夹路径
 * @param {string} inputId - 输入框ID
 */
function selectFolderManually(inputId) {
    console.log(`📝 手动输入文件夹路径: ${inputId}`);

    const textInput = document.getElementById(inputId);
    if (!textInput) {
        console.error(`❌ 找不到输入框: ${inputId}`);
        showToast('error', '输入框未找到，请刷新页面重试');
        return;
    }

    // 设置占位符和焦点
    const currentValue = textInput.value || '';
    if (!currentValue) {
        if (inputId === 'folderSourcePath') {
            textInput.placeholder = 'D:\\图片文件夹\\SKU文件夹';
            showToast('info', '请输入包含SKU文件夹的完整根目录路径 📁\n例如：D:\\图片文件夹\\SKU文件夹');
        } else if (inputId === 'folderExportPath') {
            textInput.placeholder = 'D:\\导出文件夹\\处理后的图片';
            showToast('info', '请输入导出文件的完整目标路径 📁\n例如：D:\\导出文件夹\\处理后的图片');
        } else {
            textInput.placeholder = 'D:\\文件夹路径';
            showToast('info', '请输入完整的文件夹路径 📁');
        }
        textInput.focus();
    } else {
        textInput.focus();
        textInput.select(); // 选中当前文本，方便用户修改
        showToast('info', '请修改输入框中的完整路径 ✏️');
    }
}

/**
 * 选择源文件夹路径（向后兼容）
 * @param {HTMLElement} textInput - 输入框元素
 */
function selectSourceFolderPath(textInput) {
    console.log(`📁 选择源文件夹路径: ${textInput.id}`);
    selectFolderPath('folderSourcePath');
}

/**
 * 选择导出目标路径（向后兼容）
 * @param {HTMLElement} textInput - 输入框元素
 */
function selectExportFolderPath(textInput) {
    console.log(`📁 选择导出目标路径: ${textInput.id}`);
    selectFolderPath('folderExportPath');
}

/**
 * 通用文件夹路径选择（向后兼容）
 * @param {string} inputId - 输入框ID
 * @param {HTMLElement} textInput - 输入框元素
 */
function selectManualFolderPath(inputId, textInput) {
    console.log(`📁 选择文件夹路径: ${inputId}`);
    selectFolderPath(inputId);
}

/**
 * 通用文件夹选择函数（保持向后兼容）
 * @param {string} inputId - 输入框的ID
 */
function selectFolder(inputId) {
    selectFolderPath(inputId);
}

function selectFolderSourcePath() {
    selectFolder('folderSourcePath');
}

function selectFolderExportPath() {
    selectFolder('folderExportPath');
}

function startFolderExport() {
    if (window.folderManager) {
        window.folderManager.startExport();
    }
}

// 初始化文件夹管理器
document.addEventListener('DOMContentLoaded', function() {
    window.folderManager = new FolderManager();

    // 绑定继续图片处理按钮
    const continueBtn = document.getElementById('continueImageProcessing');
    if (continueBtn) {
        continueBtn.addEventListener('click', () => {
            window.folderManager.continueImageProcessing();
        });
    }

    // 绑定打开导出文件夹按钮
    const openFolderBtn = document.getElementById('openExportFolder');
    if (openFolderBtn) {
        openFolderBtn.addEventListener('click', () => {
            showToast('info', '请手动打开导出文件夹，路径已显示在结果中');
        });
    }
});

/**
 * 显示图片重命名工具
 */
function showRenameTools() {
    console.log('🔧 显示图片重命名工具');

    // 隐藏首页内容
    const step1Container = document.getElementById('step1-container');
    if (step1Container) {
        step1Container.style.display = 'none';
    }

    // 显示工具容器
    const toolsContainer = document.getElementById('toolsContainer');
    if (toolsContainer) {
        toolsContainer.style.display = 'block';
    }

    // 激活图片重命名标签
    const renameTab = document.getElementById('rename-tab');
    const renameTabPane = document.getElementById('rename-tab-pane');

    if (renameTab && renameTabPane) {
        // 移除所有标签的激活状态
        document.querySelectorAll('.nav-link').forEach(tab => {
            tab.classList.remove('active');
            tab.setAttribute('aria-selected', 'false');
        });

        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('show', 'active');
        });

        // 激活图片重命名标签
        renameTab.classList.add('active');
        renameTab.setAttribute('aria-selected', 'true');
        renameTabPane.classList.add('show', 'active');
    }

    showToast('success', '已切换到图片重命名工具 🔧');
}

/**
 * 显示亚马逊模板填充工具
 */
function showTemplateTools() {
    console.log('📋 显示亚马逊模板填充工具');

    // 隐藏首页内容
    const step1Container = document.getElementById('step1-container');
    if (step1Container) {
        step1Container.style.display = 'none';
    }

    // 显示工具容器
    const toolsContainer = document.getElementById('toolsContainer');
    if (toolsContainer) {
        toolsContainer.style.display = 'block';
    }

    // 激活模板填充标签
    const templateTab = document.getElementById('template-tab');
    const templateTabPane = document.getElementById('template-tab-pane');

    if (templateTab && templateTabPane) {
        // 移除所有标签的激活状态
        document.querySelectorAll('.nav-link').forEach(tab => {
            tab.classList.remove('active');
            tab.setAttribute('aria-selected', 'false');
        });

        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('show', 'active');
        });

        // 激活模板填充标签
        templateTab.classList.add('active');
        templateTab.setAttribute('aria-selected', 'true');
        templateTabPane.classList.add('show', 'active');
    }

    showToast('success', '已切换到亚马逊模板填充工具 📋');
}

/**
 * 返回首页
 */
function backToStep1() {
    console.log('🏠 返回首页');

    // 显示首页内容
    const step1Container = document.getElementById('step1-container');
    if (step1Container) {
        step1Container.style.display = 'block';
    }

    // 隐藏工具容器
    const toolsContainer = document.getElementById('toolsContainer');
    if (toolsContainer) {
        toolsContainer.style.display = 'none';
    }

    showToast('info', '已返回首页 🏠');
}